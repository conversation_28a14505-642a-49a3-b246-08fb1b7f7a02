import json
from datetime import datetime
import sys
from loguru import logger
logger.remove()#删去import logger之后自动产生的handler，不删除的话会出现重复输出的现象
handler_id = logger.add(sys.stderr, level="INFO")
from openai import OpenAI, AsyncOpenAI

from typing import List, Optional, Union, Dict, AsyncGenerator, Any
from src.utils.llms.llm_base import LLMBase

# from functools import lru_cache
from expiring_lru_cache import lru_cache, MINUTES
import jwt
import time
import httpx


@lru_cache(maxsize=5)
def get_model_path_open(api_key, base_url):
    client_sync = OpenAI(api_key=api_key, base_url=base_url)
    return client_sync.models.list().data[0].id


@lru_cache(expires_after=14 * MINUTES)
def gen_api_key_nova(ak, sk):
    headers = {"alg": "HS256", "typ": "JWT"}
    payload = {
        "iss": ak,
        "exp": int(time.time())
        + 15 * 60,  # 填写您期望的有效时间，此处示例代表当前时间+30分钟
        "nbf": int(time.time()) - 5,  # 填写您期望的生效时间，此处示例代表当前时间-5秒
    }
    token = jwt.encode(payload, sk, headers=headers)
    return token


class OpenAI_LLM(LLMBase):
    def __init__(self, config: Dict[str, Any]):
        self.api_key = config.get("api_key", "")
        if "ak" in config:
            self.ak = config["ak"]
        if "sk" in config:
            self.sk = config["sk"]
        self.base_url = config.get("base_url", "")
        self.model_name = config.get("model_name", "")

        if (
            self.api_key == ""
        ):  # each request has an instance of this class whose lifespan is very short
            self.api_key = gen_api_key_nova(self.ak, self.sk)
        self.temperature = config.get("temperature", 0.1)

        self.top_p = config.get("top_p", 0.5)
        self.max_tokens = config.get("max_tokens", 4096)

        super().__init__()
        self.client = self.init_openai_clients()

    def init_openai_clients(self):
        logger.info(f"OpenAI Base URL: {self.base_url}")
        if "https" in self.base_url:
            client = AsyncOpenAI(api_key=self.api_key, base_url=self.base_url, http_client=httpx.AsyncClient(verify=False))
        else:
            client = AsyncOpenAI(api_key=self.api_key, base_url=self.base_url)
        if self.model_name == "":
            self.model_name = get_model_path_open(self.api_key, self.base_url)

        if self.model_name == "":
            logger.critical(f"cannot get model_path for endpoint {self.base_url}")

        logger.debug(
            f"self.model_name: {self.model_name}, self.base_url:{self.base_url}"
        )
        return client

    async def openai_call(
        self,
        messages,
        model_name,
        temperature: Union[float, None] = None,
        top_p: Union[float, None] = None,
        stream: bool = False,
        max_tokens: Union[int, None] = None,
    ):
        result = await self.client.chat.completions.create(
            model=model_name,
            temperature=temperature if temperature is not None else self.temperature,
            top_p=top_p if top_p is not None else self.top_p,
            messages=messages,
            stream=False,
            max_tokens=max_tokens if max_tokens is not None else self.max_tokens,
        )

        return result.choices[0].message.content

    async def yield_openai_call(
        self,
        messages,
        model_name,
        temperature: Union[float, None] = None,
        top_p: Union[float, None] = None,
        stream: bool = True,
        max_tokens: Union[int, None] = None,
    ):
        result = await self.client.chat.completions.create(
            model=model_name,
            temperature=temperature if temperature is not None else self.temperature,
            top_p=top_p if top_p is not None else self.top_p,
            messages=messages,
            stream=stream,
            stream_options={"include_usage": True},
            max_tokens=max_tokens if max_tokens is not None else self.max_tokens,
        )

        async for chunk in result:
            if chunk is None:
                continue
            if chunk.choices and chunk.choices[0].delta.content is not None:
                char = chunk.choices[0].delta.content
                # print(f"chunk: {chunk}")
                if chunk.choices[0].finish_reason == "sensitive":
                    yield {"content": char, "code": chunk.status.get('code', 18)}
                else:
                    yield {"content": char}
            elif chunk.usage is not None:
                yield {"usage": dict(chunk.usage), "content": ""}

    async def yield_chat_from_db(
        self,
        messages: List[Dict[str, str]],
        guiding_res: Optional[str] = None,
        temperature: Union[float, None] = None,
        top_p: Union[float, None] = None,
        stream: bool = True,
        max_tokens: Union[int, None] = None,
    ) -> AsyncGenerator[str, None]:
        response_generator = self.yield_openai_call(
            messages=messages,
            model_name=self.model_name,
            temperature=temperature if temperature is not None else self.temperature,
            top_p=top_p if top_p is not None else self.top_p,
            stream=stream,
            max_tokens=max_tokens if max_tokens is not None else self.max_tokens,
        )
        # for guiding_res_chunk in guiding_res:
        #     full_response += guiding_res_chunk
        #     yield guiding_res_chunk
        async for chunk in response_generator:
            yield chunk

    async def yield_chat(
        self,
        messages: List[Dict[str, str]],
        temperature: Union[float, None] = None,
        top_p: Union[float, None] = None,
        stream: bool = True,
        max_tokens: Union[int, None] = None,
    ):
        self.stream = stream

        response = self.yield_openai_call(
            messages,
            model_name=self.model_name,
            temperature=temperature if temperature is not None else self.temperature,
            top_p=top_p if top_p is not None else self.top_p,
            stream=stream,
            max_tokens=max_tokens if max_tokens is not None else self.max_tokens,
        )
        logger.debug(f"response:   {response}")
        return response

    async def chat(
        self,
        messages: List[Dict[str, str]],
        temperature: Union[float, None] = None,
        top_p: Union[float, None] = None,
        stream: bool = True,
        max_tokens: Union[int, None] = None,
    ) -> str:
        self.stream = stream

        response = await self.openai_call(
            messages,
            model_name=self.model_name,
            temperature=temperature if temperature is not None else self.temperature,
            top_p=top_p if top_p is not None else self.top_p,
            stream=stream,
            max_tokens=max_tokens if max_tokens is not None else self.max_tokens,
        )
        logger.debug(f"response:   {response}")
        return response
