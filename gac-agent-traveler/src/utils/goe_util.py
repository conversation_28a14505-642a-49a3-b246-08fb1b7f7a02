import requests, aiohttp
from typing_extensions import Optional
from configuration import config
from schemas.models import ErrorDataModel


api_key = config["gaode"]["api_key"]
url_regeo = config["gaode"]["regeo"]["url"]


def _request_re_geo(lat: str, lon: str) -> [Optional[dict], Optional[ErrorDataModel]]:
    from src.traveler_exception import error_code_location_http_error, error_code_location_return_error
    try:
        location = format(float(lon), ".6f") + "," + format(float(lat), ".6f")
    except ValueError as e:
        print(f"Invalid latitude or longitude: {e}")
        return None, ErrorDataModel(error_code=error_code_location_http_error, error_message="Invalid latitude or longitude")

    error_data = None
    param = {"key": api_key, "location": location, "radius": "100", "output": "JSON"}
    session = requests.Session()
    try:
        with session.post(url=url_regeo, params=param) as response:
            print(f"!!!!!!!!!!!!!!!!!!!!    {response.status_code}")
            if response.status_code == 200:
                return response.json(), None
            else:
                error_data = ErrorDataModel(error_code=error_code_location_return_error, error_message=f"Request location status_code: {response.status_code}")
    except requests.RequestException as e:
        error_data = ErrorDataModel(error_code=error_code_location_http_error, error_message=f"Request location failed: {str(e)}")
        print(f"Request failed: {e}")
    finally:
        session.close()
    return None, error_data
    

async def request_re_geo_rough(lat: str, lon: str)-> [Optional[str], Optional[ErrorDataModel]]:
    position_json, error_data = _request_re_geo(lat=lat, lon=lon)
    if not position_json:
        return None, error_data
    from src.traveler_exception import error_code_location_format_error
    if isinstance(position_json, dict):
        if "1" == position_json["status"] and position_json["regeocode"]:
            regeocode = position_json["regeocode"]
            position = ""
            addressComponent = regeocode["addressComponent"]
            position += addressComponent.get("province", "")
            city_list = addressComponent.get("city")
            if isinstance(city_list, str):
                position += city_list
            elif isinstance(city_list, list) and 0 < len(city_list) and isinstance(city_list[0], str):
                position += city_list[0]
            return position, None
        else:
            return None, ErrorDataModel(error_code=error_code_location_format_error, error_message=f"Failed to geocode position_json: {position_json}")
    else:
        return None, ErrorDataModel(error_code=error_code_location_format_error, error_message=f"Failed to geocode position_json: {position_json}")

async def request_re_geo_detail(lat: str, lon: str)-> Optional[str]:
    position_json, error_data = _request_re_geo(lat=lat, lon=lon)
    if not position_json:
        return None
    if isinstance(position_json, dict):
        if "1" == position_json["status"] and position_json["regeocode"]:
            regeocode = position_json["regeocode"]
            position = ""
            addressComponent = regeocode["addressComponent"]
            position += addressComponent.get("province", "")
            city_list = addressComponent.get("city")
            if isinstance(city_list, str):
                position += city_list
            elif isinstance(city_list, list) and 0 < len(city_list) and isinstance(city_list[0], str):
                position += city_list[0]
            position += addressComponent.get("district", "")
            position += addressComponent.get("township", "")
            neighborhood = addressComponent["neighborhood"]
            building = addressComponent["building"]
            streetNumber = addressComponent["streetNumber"]
            if streetNumber: 
                position += streetNumber.get("street", "")
                position += streetNumber.get("number", "")
            elif neighborhood:
                position += neighborhood.get("name", "")
            elif building:
                position += building.get("name", "")
            return position
        else:
            return None
    else: 
        return None