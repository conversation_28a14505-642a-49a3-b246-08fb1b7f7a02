import json
from src.utils.actions.tool_actions.web_search_base import WebSearchBase
import async<PERSON>
from tencentcloud.common.common_client import CommonClient
from tencentcloud.common import credential
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile


class Tencent(WebSearchBase):
    def __init__(self, config, engine_name="tencent"):
        super().__init__()
        httpProfile = HttpProfile()
        httpProfile.endpoint = "tms.tencentcloudapi.com"
        self.engine_name = engine_name
        self.clientProfile = ClientProfile(httpProfile=httpProfile)
        self.config = config
        self.cred = credential.Credential(config["ak"], config["sk"])
    # async def search(self, query: str, engine: Optional[str] = None):
    #     try:
    #           # 填充客户的AK Sk即可
    #         params = {
    #             "Query": query
    #         }
    #         common_client = CommonClient("tms", "2020-12-29", self.cred, "", profile=self.clientProfile)
    #         search_res = common_client.call_json("searchPro", params)
    #         # print(search_res['Response'])
    #         format_res = self.format_search_res(search_res['Response'])
    #         return format_res
    #     except TencentCloudSDKException as err:
    #         return {
    #             "error": f"Response failed with error: {err.message}"
    #         }

    def sync_search(self, query):
        params = {"Query": query}
        cli = CommonClient("tms", "2020-12-29", self.cred, "", profile=self.clientProfile)
        return cli.call_json("searchPro", params)

    async def search(self, query: str, num: int):
        try:
            res = await asyncio.to_thread(self.sync_search, query)
            return self.format_search_res(res["Response"], num)
        except TencentCloudSDKException as e:
            return {"error": f"Response failed: {e}"}

    def format_search_res(self, search_res: dict, num: int):
        result = {"answerBox": {
            "title": "",
            "snippet": "",
            "link": "",
            "cached_link": "",
            "date": "",
            "source_from": "",
        }}
        organic_results = search_res.get("Pages", "")
        if organic_results:
            for idx, entry in enumerate(search_res["Pages"], start=1):
                if idx > num + 1: break
                entry = json.loads(entry)
                date = entry.get("date", "")
                if date and "T" in date:
                    date = date.split("T")[0]
                result[idx] = {
                    "title": entry.get("title", ""),
                    "snippet": entry.get("passage", ""),
                    "link": entry.get("url", ""),
                    "cached_link": entry.get("cachedPageUrl", ""),
                    "date": date,
                    "source_from": entry.get("site", ""),
                }
        return result
