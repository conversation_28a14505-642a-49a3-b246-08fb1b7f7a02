import re, json, ast
from typing_extensions import Optional
from loguru import logger

class ContentHelper:

    def __init__(self):
        self.llm_total_responds = ""
        self.poi_list_str = ""
        self.has_poi_list = False
        self.tts_start = False
        self.tts_end = False
        self.tts_content = ""

    def append_content(self, part: str) -> Optional[str]:
        to_return = None
        count = self.llm_total_responds.count("\u001F")
        count_enter = self.llm_total_responds.count("\n")
        if count == 1 and 2 >= count_enter:
            if not self.tts_start:
                to_return = self.llm_total_responds.split("\u001F", 1)[-1] + part
                self.tts_start = True
            else:
                to_return = part
            self.tts_content = self.tts_content + to_return
        self.llm_total_responds = self.llm_total_responds + part
        return to_return


    def get_poi_list(self) -> Optional[list]:
        list_pattern = r'景点列表：\[(.*?)\]'
        try:
            match = re.search(list_pattern, self.llm_total_responds)
            self.has_poi_list = True
            self.poi_list_str = match.group(1)
            return [s.strip().strip('"') for s in match.group(1).split(',')]
        except Exception as e:
            pass
        return None

    def poi_detail_dict(self) -> Optional[dict]:
        try:
            total_responds = str(self.llm_total_responds)
            total_responds = total_responds.replace("\n", "")
            # 1. 提取 ```json{...}``` 代码块
            logger.info(f"get poi detail source-total_responds : {total_responds}")
            json_block = re.search(r'json```({.*?})```', total_responds, re.S).group(1)
            logger.info(f"get poi detail json: {json_block}")
            result = None
            try:
                result = json.loads(json_block)
            except Exception as e:
                pass
            if not result:
                fixed = re.sub(r'([{,]\s*)([^"\s][^:]*)(\s*:)', r'\1"\2"\3', json_block)
                result = ast.literal_eval(fixed)
            if isinstance(result, dict):
                return result
        except Exception as e:
            print(e)
        return None


    def get_tts_content_after_end(self) -> str:
        return self.tts_content