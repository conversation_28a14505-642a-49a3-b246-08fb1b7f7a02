import json
import queue
import secrets
import time
import uuid
import asyncio

from fastapi import APIRouter, Response
from typing_extensions import Union, Optional
from loguru import logger
from schemas.search import WebSearchReqGuangqiDifyV1 as RequestModel
from schemas.search import LocationReq, UserMemoryReq
from service.logger_manager import fire_logger
from src.utils.llms.openai_SDK import OpenAI_LLM

from src.utils.actions.other_actions.rerank_topk import Rerank
from src.utils.actions.tool_actions.action_excutor import ActionExecutor
from src.utils.actions.tool_actions.web_search import WebSearch
from src.utils.memory.memory_base import AllMessageMemory
from src.utils.web_content_scrape import WebScrape, WebScrapePy
from src.utils.poi_info_util import get_all_brief_info

from sse_starlette.sse import EventSourceResponse as StreamResponse
from starlette.responses import JSONResponse as RestfulResponse
from travel_yield_demo import WebSearchAgent as YieldSearchAgent, TravelChatQueryRequest

from src.utils.actions.tool_actions.bing import Bing
from src.utils.actions.tool_actions.serper import Serper
from src.utils.actions.tool_actions.tencent import Tencent

from configuration import config
from schemas.models import ErrorDataModel, LocationResultModel, MemResultModel

global_config = config
selected_llm_config = global_config["llm_server_set"][
    global_config["selected_llm_server"]
]
serper_se = Serper(global_config["search_engine"]["serper"], "google")
bing_se = Bing(bing_config=global_config["search_engine"]["bing"], engine_name="bing")
tencent_se = Tencent(config=global_config["search_engine"]["tencent"], engine_name="tencent")
rewrite_llm_config = selected_llm_config.copy()  # shallow copy
for k in global_config["rewrite"].keys():  # some override parameters
    rewrite_llm_config[k] = global_config["rewrite"][k]


api_version = "1.0.22"
DEFAULT_LOCATION = "北京"
router_for_gac_dify = APIRouter(prefix="/gac-agent-traveler")



class PromptInfo:
    def __init__(self, rewrite_prompt_system, rewrite_prompt_user):
        self.rewrite_prompt_system = rewrite_prompt_system
        self.rewrite_prompt_user = rewrite_prompt_user


class ExtraInfo:

    def __init__(self, text_location: Optional[str] = DEFAULT_LOCATION,
                 habit: Optional[str] = None,
                 total_time: float = 0.0,
                 location_time: float = 0.0,
                 habit_time: float = 0.0,
                 error_list: Optional[list] = None,
                 ):
        # 中文的地址信息
        self.text_location = text_location
        # 从记忆体中取出的偏好信息
        self.habit = habit
        self.total_time = total_time
        self.location_time = location_time
        self.habit_time = habit_time
        self.error_list = error_list


# 选取使用的搜索引擎
def get_search_engine_tool(request: RequestModel, use_search_cache: bool) -> list:
    engine = request.engine
    if engine == 'google':
        return [WebSearch(search_engines=[serper_se], use_search_cache=use_search_cache)]
    elif engine == 'bing':
        return [WebSearch(search_engines=[bing_se], use_search_cache=use_search_cache)]
    else:
        return [WebSearch(search_engines=[tencent_se], use_search_cache=use_search_cache)]


# 提取当前query
def get_query_from_request(request: RequestModel) -> str:
    if request.query is not None:
        return request.query
    else:
        if request.messages and 0 < len(request.messages):
            return request.messages[-1].content
        else:
            raise ValueError('query and messages is BOTH None')


# 处理历史
def process_memories(request: RequestModel) -> AllMessageMemory:
    public_memory = AllMessageMemory()
    # 处理历史
    if request.history is not None:
        for v in request.history:
            logger.debug(f"{v}  type: {type(v)}")
            public_memory.add_message(v.content, message_type=v.role)
    elif request.messages is not None:
        # 兼容OpenAI API
        for v in request.messages[:-1]:
            public_memory.add_message(v.content, message_type=v.role)
    else:
        # 没有历史对话
        pass
    return public_memory


# 构造爬虫
def build_crawler() -> Union[WebScrape, WebScrapePy]:
    limit_scraping_time = "800ms"
    if global_config["scrape"]["engine-in-use"] == "go-readability":
        return WebScrape(
            global_config["scrape"], limit_scraping_time=limit_scraping_time
        )
    elif global_config["scrape"]["engine-in-use"] == "python-document":
        return WebScrapePy()
    else:
        raise ValueError(
            f"unknown scrape engine: {global_config['scrape']['engine-in-use']}"
        )


# 处理非流式请求
async def common_post_restful(request: RequestModel, prompt: PromptInfo, extra_info: ExtraInfo, start_time: float):
    request_id = uuid.uuid4()
    response_header = {"request_id": str(request_id)}
    fire_logger.info("%s START", str(request_id))

    try:
        k = request.k
        detect = request.detect
        use_search_cache = request.use_search_cache
        message_id = secrets.token_hex(7)
        # 选取使用的搜索引擎
        tools = get_search_engine_tool(request, use_search_cache)
        multimodal = request.surrounding
        if not multimodal:
            multimodal = ""

        # logger.info(f"selected search engine: {engine}")
        rerank_module = Rerank(k, global_config["rerank"]["base_url"])
        tool_executor = ActionExecutor(tools)

        # 处理历史
        public_memory = process_memories(request)
        # 提取当前query
        current_query = get_query_from_request(request)

        # 构造时生成api_key，对于nova的key，有过期时间，所以需要每条request都构造一次，生成key有cache进行加速
        rewrite_client = OpenAI_LLM(rewrite_llm_config)
        summary_client = OpenAI_LLM(selected_llm_config)
        # 构造爬虫
        crawler = build_crawler()

        web = YieldSearchAgent(
            rewrite_client,
            summary_client,
            rerank_module,
            tool_executor,
            public_memory,
            crawler,
            None,
            detect,
            global_config["sensitive"],
            router_system_prompt=prompt.rewrite_prompt_system,
            router_user_prompt=prompt.rewrite_prompt_user,
        )

        web_chat_query_request = TravelChatQueryRequest(
            query=current_query,
            message_id=message_id,
            request_id=str(request_id),
            model_name="kami-search",
            resource="/simple/web_search/v2",
            user_portrait=extra_info.habit,
            position=extra_info.text_location,
            multimodal=multimodal
        )  # TODO remove the model_name hardcode

        response = {}
        answer = ""
        first_word = True
        time_cost = {'t1-loc': extra_info.location_time, 't2-mem': extra_info.habit_time}
        interceptor_queue = queue.Queue()
        async for result in web.chat(web_chat_query_request, interceptor_queue):
            if isinstance(result, dict) and result.get("type") == "t3-intent":
                time_cost["t3-intent"] = round(time.perf_counter() - start_time, 4)
            if isinstance(result, dict) and result.get("type") == "t4-src":
                time_cost["t4-src"] = round(time.perf_counter() - start_time, 4)
            if isinstance(result, dict) and result.get("type") == "t5-fetch":
                time_cost["t5-fetch"] = round(time.perf_counter() - start_time, 4)
            if isinstance(result, dict) and result.get("type") == "t6-mft":
                time_cost["t6-mft"] = round(time.perf_counter() - start_time, 4)
            if isinstance(result, dict) and result.get("type") == "t7-sft":
                time_cost["t7-sft"] = round(time.perf_counter() - start_time, 4)
            if isinstance(result, dict) and result.get("type") == "message":
                answer += result["data"]
                if first_word is True:
                    first_word = False
                    time_cost["TTFT"] = round(time.perf_counter() - start_time, 4)
                if "usage" in result:  # may only appear in the last result
                    response["usage"] = result["usage"]
            if isinstance(result, dict) and result.get("type") == "error":
                answer = result.get("data")
                response['code'] = result.get("code")
            if isinstance(result, dict) and result.get("type") == "messageEnd":
                if response.get('code', None) is None:
                    response['code'] = result.get("code", 0)
                # if response['code'] != 0:
                #     answer += result["data"]

        time_cost["total_time"] = round(time.perf_counter() - start_time, 4)
        response["answer"] = answer
        response["time_cost"] = time_cost
        # fire_logger.info("%s FINISH", str(request_id), extra={"content": response})
        logger.info(f"{str(request_id)} FINISH")

        # # create async tasks to write messages to the DB
        # if "usage" in response:
        #     input_tokens = response["usage"]["prompt_tokens"]
        #     output_tokens = response["usage"]["completion_tokens"]
        # else:
        #     input_tokens, output_tokens = 0, 0

        return RestfulResponse(response, headers=response_header)
    except Exception as e:
        logger.error(e)
        import traceback

        traceback.print_exc()
        response = {"error": str(e)}
        return RestfulResponse(response, headers=response_header)


async def common_post_stream(request: RequestModel, prompt: PromptInfo, extra_info: ExtraInfo, start_time: float):
    request_id = uuid.uuid4()
    response_header = {"request_id": str(request_id)}
    fire_logger.info("%s START", str(request_id))
    logger.info(f"extra_info: {vars(extra_info)}")

    try:
        k = request.k
        detect = request.detect
        message_id = secrets.token_hex(7)
        use_search_cache = request.use_search_cache
        # 选取使用的搜索引擎
        tools = get_search_engine_tool(request, use_search_cache)
        lon = request.location.lon
        lat = request.location.lat
        multimodal = request.surrounding
        if not multimodal:
            multimodal = ""

        # logger.info(f"selected search engine: {engine}")
        rerank_module = Rerank(k, global_config["rerank"]["base_url"])
        tool_executor = ActionExecutor(tools)

        # 处理历史
        public_memory = process_memories(request)
        # 提取当前query
        current_query = get_query_from_request(request)

        # 构造时生成api_key，对于nova的key，有过期时间，所以需要每条request都构造一次，生成key有cache进行加速
        rewrite_client = OpenAI_LLM(rewrite_llm_config)
        summary_client = OpenAI_LLM(selected_llm_config)
        # 构造爬虫
        crawler = build_crawler()

        web = YieldSearchAgent(
            rewrite_client,
            summary_client,
            rerank_module,
            tool_executor,
            public_memory,
            crawler,
            None,
            detect,
            global_config["sensitive"],
            router_system_prompt=prompt.rewrite_prompt_system,
            router_user_prompt=prompt.rewrite_prompt_user,
        )

        web_chat_query_request = TravelChatQueryRequest(
            query=current_query,
            message_id=message_id,
            request_id=str(request_id),
            model_name="kami-search",
            resource="/simple/web_search/v2",
            user_portrait=extra_info.habit,
            position=extra_info.text_location,
            multimodal = multimodal
        )  # TODO remove the model_name hardcode

        async def event_generator():
            result = {
                # "data": chat_id.hex,
                "type": "chat_id",
                "messageId": str(message_id),
            }
            ## disable chat_id yield for TTFT testing
            # yield dict(data=json.dumps(result, ensure_ascii=False))
            answer = ""
            last_message = None
            first_word = True
            time_cost = {'t1-loc': extra_info.location_time, 't2-mem': extra_info.habit_time}
            extra_info_error_list = extra_info.error_list
            if isinstance(extra_info_error_list, list):
                for error_item in extra_info_error_list:
                    if isinstance(error_item, ErrorDataModel):
                        yield dict(data=json.dumps({
                            "type": "error",
                            "code": error_item.error_code,
                            "data": error_item.error_message,
                            "messageId": str(message_id),
                        }, ensure_ascii=False))
            interceptor_queue = queue.Queue()
            async for result in web.chat(web_chat_query_request, interceptor_queue):
                # and result.get("type") == "message":
                if isinstance(result, dict) and result.get("type") != "messageEnd":
                    yield dict(data=json.dumps(result, ensure_ascii=False))
                    if isinstance(result, dict) and result.get("type") == "pre_intent":
                        time_cost["t3-pre-intent"] = round(time.perf_counter() - start_time, 4)
                    if isinstance(result, dict) and result.get("type") == "t3-intent":
                        time_cost["t3-intent"] = round(time.perf_counter() - start_time, 4)
                    if isinstance(result, dict) and result.get("type") == "t4-src":
                        time_cost["t4-src"] = round(time.perf_counter() - start_time, 4)
                    if isinstance(result, dict) and result.get("type") == "t5-fetch":
                        time_cost["t5-fetch"] = round(time.perf_counter() - start_time, 4)
                    if isinstance(result, dict) and result.get("type") == "t6-mft":
                        time_cost["t6-mft"] = round(time.perf_counter() - start_time, 4)
                    if isinstance(result, dict) and result.get("type") == "t7-sft":
                        time_cost["t7-sft"] = round(time.perf_counter() - start_time, 4)
                    if isinstance(result, dict) and result.get("type") == "poi_list" and isinstance(result.get("data"), list):
                        poi_list = result.get("data")
                        time_cost["t8-list"] = round(time.perf_counter() - start_time, 4)
                        asyncio.create_task(get_all_brief_info(poi_list, lon=lon, lat=lat, interceptor_queue=interceptor_queue, message_id = message_id))
                    if isinstance(result, dict) and result.get("type") == "brief_info":
                        time_cost["t9-brief"] = round(time.perf_counter() - start_time, 4)
                    if isinstance(result, dict) and result.get("type") == "detail_info":
                        time_cost["t10-detail"] = round(time.perf_counter() - start_time, 4)
                    if isinstance(result, dict) and result.get("type") == "follow_up":
                        time_cost["t11-follow"] = round(time.perf_counter() - start_time, 4)
                    if result["type"] == "message":
                        answer += result["data"]
                        last_message = result
                        if first_word is True:
                            first_word = False
                            time_cost["TTFT"] = round(time.perf_counter() - start_time, 4)
                else:
                    # yield messageEnd with time_cost summary
                    time_cost["total_time"] = round(time.perf_counter() - start_time, 4)
                    result["time_cost"] = time_cost
                    yield dict(data=json.dumps(result, ensure_ascii=False))
            # create async tasks to write messages to the DB
            if last_message and "usage" in last_message:
                input_tokens = last_message["usage"]["prompt_tokens"]  # type: ignore
                output_tokens = last_message["usage"]["completion_tokens"]  # type: ignore

        return StreamResponse(event_generator(), headers=response_header)
    except Exception as e:
        logger.error(e)
        import traceback

        traceback.print_exc()
        response = {"error": str(e)}
        return RestfulResponse(response, headers=response_header)

async def require_text_location(source: Optional[LocationReq], start_time: float, time_limit: float) -> LocationResultModel :
    if not source:
        return LocationResultModel(result=DEFAULT_LOCATION, time_cost=round(time.perf_counter() - start_time, 4), error_info=None)

    async def require_location(source: LocationReq):
        text_location = None
        error_data = None
        from src.utils.cache import rd
        key = f"text_location:{source.build_coordinate_text()}"
        try:
            text_location = await rd.get(key)
            if text_location:
                text_location = text_location.decode('utf-8')
                logger.info(f"require_location from redis succeed, location: {text_location}")
        except BaseException as e:
            logger.error(f"require_location from cache failed, e: {e}")
        if not text_location:
            from src.utils.goe_util import request_re_geo_rough
            text_location, error_data = await request_re_geo_rough(lat=source.lat, lon=source.lon)
            if text_location:
                logger.info(f"query from regeo location: {text_location}")
                await rd.setex(key, 3600 * 3, text_location)
        return text_location, error_data

    try:
        result, error_data = await asyncio.wait_for(require_location(source), timeout=time_limit)
        if not result:
            result = DEFAULT_LOCATION
        return LocationResultModel(result=result, time_cost=round(time.perf_counter() - start_time, 4), error_info=error_data)
    except:
        logger.error("require_text_location timeout")
        return LocationResultModel(result=DEFAULT_LOCATION, time_cost=round(time.perf_counter() - start_time, 4), error_info=None)


async def require_user_habit(source: Optional[UserMemoryReq], start_time: float, time_limit: float) -> MemResultModel:
    if not source or not source.car_id or not source.user_id or 0 >= len(source.category):
        return MemResultModel(result="", time_cost=round(time.perf_counter() - start_time, 4), error_info=None)

    async def require_habit_from_cache(source: UserMemoryReq):
        body_json = {
            "car_id": source.car_id,
            "face_id": source.user_id,
            "categories": source.category
        }
        error_data = None
        from src.utils.user_habit_util import request_habit_from_user_memory_text
        result_str, error_data = await request_habit_from_user_memory_text(body=body_json, relation_filter="LIKES", time_limit=time_limit)
        if not result_str:
            result_str = ""
        return result_str, error_data

    try:
        result, error_data = await asyncio.wait_for(require_habit_from_cache(source), timeout=time_limit)
        return MemResultModel(result=result, time_cost=round(time.perf_counter() - start_time, 4), error_info=error_data)
    except BaseException as e:
        logger.error(f"require_habit_from_cache timeout, e: {e}")
        return MemResultModel(result="", time_cost=round(time.perf_counter() - start_time, 4), error_info=None)

async def obtain_extra_info(request):
    start_time = time.perf_counter()
    logger.info("loc start")
    location_model = await require_text_location(request.location, start_time, 0.3)
    text_location = location_model.result
    location_cost = location_model.time_cost
    location_error_data = location_model.error_info
    logger.info(f"loc end-----cost time:{location_cost}-----location:{text_location}")
    logger.info("mem start")
    habit_model = await require_user_habit(request.user_info, start_time, 0.3)
    habit = habit_model.result
    habit_cost = habit_model.time_cost
    habit_error_data = habit_model.error_info
    logger.info(f"mem end-----cost time:{round(habit_cost - location_cost, 4)}-----habit:{habit}")
    total_time = round(time.perf_counter() - start_time, 4)
    logger.info(f"city: {text_location} habit: {habit}")

    error_list = []
    if location_error_data:
        error_list.append(location_error_data)
    if habit_error_data:
        error_list.append(habit_error_data)
    return ExtraInfo(text_location=text_location, habit=habit,
                     total_time=total_time, location_time=location_cost, habit_time=habit_cost, error_list=error_list)


@router_for_gac_dify.get("/v1/health")
async def health() -> Response:
    """Health check."""
    response_body = {"status": "ok", "version": api_version}
    response_content = json.dumps(response_body).encode("utf-8")
    response_header = {"Content-Type": "application/json"}
    return Response(status_code=200, content=response_content, headers=response_header)

@router_for_gac_dify.post("/v1/ai_traveler")
async def call_post_ai_traveler(request: RequestModel):
    start_time = time.perf_counter()
    logger.debug("call_post_ai_traveler")
    from src.prompts.guanqgi_dify.ai_traveler import rewrite_prompt_system, rewrite_prompt_user, \
        rewrite_prompt_system_7b, rewrite_prompt_user_7b
    # rewrite过程是否使用7b小模型
    # default is 72b
    use_7b_rewrite_model = "type" in rewrite_llm_config and rewrite_llm_config["type"] == "7b"
    if use_7b_rewrite_model:
        prompt: PromptInfo = PromptInfo(rewrite_prompt_system=rewrite_prompt_system_7b,
                                        rewrite_prompt_user=rewrite_prompt_user_7b)
    else:
        prompt: PromptInfo = PromptInfo(rewrite_prompt_system=rewrite_prompt_system,
                                        rewrite_prompt_user=rewrite_prompt_user)

    extra_info = await obtain_extra_info(request)

    if request.stream:
        return await common_post_stream(request=request, prompt=prompt, extra_info=extra_info, start_time=start_time)
    else:
        return await common_post_restful(request=request, prompt=prompt, extra_info=extra_info, start_time=start_time)