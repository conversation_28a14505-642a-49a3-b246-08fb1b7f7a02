import asyncio
import queue
import uuid
from datetime import datetime
from typing import Optional
from typing import Union, Dict, List
import time
import openai
from loguru import logger
from starlette.concurrency import run_in_threadpool
import requests
from dataclasses import dataclass, field
import json
from random import sample
# from anyio import to_thread, CapacityLimiter

from nova.security_policy import (
    TextModerationRequest,
    stream_output_moderate_text,
    moderate_text_generic,
)
# from service.logger_manager import fire_logger
from src.prompts.follow_up_prompts import follow_up_prompts
from src.prompts.conclusion_title_prompts import conclusion_prompts
from src.prompts.guide_prompts import guide_user_prompt_zh, guide_system_prompt_zh
from src.prompts.router_prompts import router_user_zh, router_system_zh
from src.prompts.pre_intent_prompts import (
    pre_intent_user_zh,
    travel_pre_intent_system_zh
)
from src.prompts.summary_prompts import (
    summary_system_prompt_zh,
    summary_user_prompt_zh,
)
from src.prompts.travel_summary_prompts import (
    travel_search_summary_system_prompt_zh_v1,
    travel_search_summary_user_prompt_zh,
)
from src.utils.actions.other_actions.topk_base import TopkBase
from src.utils.actions.tool_actions.action_excutor import ActionExecutor
from src.utils.actions.tool_actions.web_search import WebSearch
from src.utils.llms.openai_SDK import OpenAI_LLM
from src.utils.memory.memory_base import MemoryBase
from src.utils.utils import (
    stage0_parse_intent,
    stage1_format2query_with_link,
    stage3_format_web_fetch,
)
from src.utils.web_content_scrape import WebScrape, WebScrapePy
from schemas.models import ErrorDataModel, PreIntentResultModel, RewriteResultModel, FollowUpQuestionResultModel, ConclusionTitleResultModel


@dataclass
class TravelChatQueryRequest:
    query: str
    message_id: str
    user_portrait: Optional[str] = None
    position: Optional[str] = "上海"
    location: Optional[dict] = field(default_factory=lambda: {"lat": 0, "lon": 0})
    multimodal: Optional[str] = ""
    request_id: Optional[str] = None
    model_name: Optional[str] = None
    resource: str = "default"


def poi_search(location, keywords):
    # 1. 目标 URL
    URL = "https://sit-platform.senseauto.com/gac/travel-companion/v2/chat"

    # 2. 请求头
    HEADERS = {
        "client-id": "1s3963nw8802M4O55yMuU6x37tOYQ682",
        # requests 会在使用 json= 时自动补上 Content‑Type: application/json:contentReference[oaicite:1]{index=1}
    }

    # 3. 请求体 —— 直接按字典写更安全
    payload = {
        "location": location,
        "model_name": "SenseAutoChat-30B",
        "messages": [{"role": "user", "content": keywords}],
        "is_stream": True
    }

    logger.info(f"poi_search      ---payload:{payload}")
    try:
        # 4. 发送 POST 请求。stream=True 开启流式读取:contentReference[oaicite:2]{index=2}
        poi_list = []
        with requests.post(URL, headers=HEADERS, json=payload,
                           stream=True, timeout=30) as resp:
            resp.raise_for_status()  # 如果返回非 2xx 会抛异常

            # 5. 逐行打印服务端推送内容
            for line in resp.iter_lines(decode_unicode=True):
                if "data" in line:  # 跳过 keep‑alive 空行
                    res = json.loads(line[6:])["data"]["choices"][0]
                    if "poi_list" in res.keys():
                        poi_list = res["poi_list"]
                        break
                # if line:                  # 跳过 keep‑alive 空行
                #     poi_list = json.loads(line)["poi_list"]
        if len(poi_list) == 0: return

        if len(poi_list) > 5:
            poi_list = sample(poi_list, 5)

        result = {}
        for poi in poi_list:
            result[poi["name"]] = {
                "view_name": poi["name"],
                "view_tag": poi["tags"],
                "view_diatance": poi["distance"],
                "view_takings": poi["openTime"],
                "view_point": poi["rating"],
                "view_jwd": [poi["longitude"], poi["latitude"]],
                "view_address": poi["address"],
                "view_picture": poi["photos"],
                "view_cost": poi["cost"]
            }
        return result


    except requests.exceptions.RequestException as e:
        print("请求失败:", e)
        return []


class WebSearchAgent:
    def __init__(
            self,
            intent_llm: OpenAI_LLM,
            summary_llm: OpenAI_LLM,
            topk_method: Union[TopkBase, None],
            action_executor: ActionExecutor,
            public_memory: MemoryBase,
            crawler: Union[WebScrape, WebScrapePy],
            dojo: None = None,
            detect: bool = False,
            sensitive_config: Optional[Dict[str, str]] = None,
            search_nums: int = 10,
            rest_k: int = 5,
            using_cached_link_first: bool = False,
            reranker_using_title: bool = False,
            truncate_length: int = 1500,
            router_system_prompt: str = router_system_zh,
            router_user_prompt: str = router_user_zh,
    ) -> None:
        self.action_executor = action_executor
        self.intent_llm = intent_llm
        self.summary_llm = summary_llm
        self.pre_intent_llm = summary_llm
        self.topk_method = topk_method
        self.crawler = crawler
        self.public_memory = public_memory
        self.detect = detect
        self.dojo = dojo
        if sensitive_config is None:
            self.sensitive_config = {}
        else:
            self.sensitive_config = sensitive_config
        self.rest_k = rest_k
        self.search_nums = search_nums
        self.using_cached_link_first = using_cached_link_first
        self.reranker_using_title = reranker_using_title
        self.truncate_length = truncate_length
        self.router_system_prompt = router_system_prompt
        self.router_user_prompt = router_user_prompt

    async def stage0_pre_intent(self, query) -> PreIntentResultModel:
        pre_intent_user_prompt = pre_intent_user_zh.format(user_input=query)

        pre_intent_system_prompt = travel_pre_intent_system_zh

        messages = (
                [{"role": "system", "content": pre_intent_system_prompt}]
                + self.public_memory.get_past_messages()
                + [{"role": "user", "content": pre_intent_user_prompt}]
        )

        res = None
        error_data = None
        try:
            res = await self.pre_intent_llm.chat(
                messages,
                stream=False,
            )
        except BaseException as e:
            from src.traveler_exception import error_code_pre_intent_http_error
            error_data = ErrorDataModel(error_code=error_code_pre_intent_http_error, error_message=f"Fail to finish pre intent, e: {str(e)}")

        return PreIntentResultModel(result=res, error_info=error_data)

    async def stage0_intent_rewrite(self, query, position="北京", user_portrait=None, multimodal="") -> RewriteResultModel:
        input_query = self.router_user_prompt.format(user_input=query, user_portrait=user_portrait)

        if multimodal:
            print(multimodal)
            router_system_prompt = self.router_system_prompt.format(
                position=position, formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"), multimodal=multimodal
            )
        else:
            router_system_prompt = self.router_system_prompt.format(
                position=position, formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S") , multimodal=""
            )
        router_system_prompt_to_log = str(router_system_prompt)
        router_system_prompt_to_log = router_system_prompt_to_log.replace("\n", "")
        logger.info(f"router_system_prompt_to_log : {router_system_prompt_to_log}")
        # logger.error(self.public_memory)
        messages = (
                [{"role": "system", "content": router_system_prompt}]
                + self.public_memory.get_past_messages()
                + [{"role": "user", "content": input_query}]
        )

        error_data = None
        intent_return = "null"
        try:
            intent_return = await self.intent_llm.chat(
                messages,
                stream=False,
            )
        except BaseException as e:
            from src.traveler_exception import error_code_rewrite_http_error
            error_data = ErrorDataModel(error_code=error_code_rewrite_http_error, error_message=f"Fail to call rewrite model, e: {str(e)}")

        intent_json_output = stage0_parse_intent(query, intent_return)
        return RewriteResultModel(result=intent_json_output, error_info=error_data)

    async def stage0_knowledge(self, query):
        pass

    async def stage0_guide(self, query):
        guide_user_prompt = guide_user_prompt_zh.format(user_input=query)

        guide_system_prompt = guide_system_prompt_zh.format(
            formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )

        messages = (
                [{"role": "system", "content": guide_system_prompt}]
                + self.public_memory.get_past_messages()
                + [{"role": "user", "content": guide_user_prompt}]
        )
        logger.info(f"stage0_guide messages:   {messages}")

        res = await self.intent_llm.chat(
            messages,
            stream=False,
        )

        return res

    async def stage1_web_search(self, search_queries):
        """
        Perform asynchronous web searches for a list of queries, USING MULTI search engine if theres more than one.

        Args:
        - search_queries: A list of query strings. Each string represents a search query to be executed.

        e.g.
        search_queries: [商汤 股票 价格，商汤 股票 2024]

        Returns:
        - A dictionary where the keys are the original search queries and the values are the corresponding search results.

        self.action_executor("web_search", single_query) searchs each query using multiple search engine (if), see .
        """
        error_data = None
        temp_results = {}
        try:
            results = await asyncio.gather(
                *(
                    self.action_executor("web_search", query, self.search_nums)
                    for query in search_queries
                )
            )

            temp_results = {query: result for query, result in zip(search_queries, results)}
        except BaseException as e:
            from src.traveler_exception import error_code_src_search_http_error
            error_data = ErrorDataModel(error_code=error_code_src_search_http_error,
                                        error_message=f"Fail to search on web, e: {str(e)}")
        """
        temp_results:
        {
            "商汤 股票 价格": {
                "engine1": {
                    answerBox:{title:"",link:"",date,:"",from:""}, 1: {title:"",link:"",date,:"",from:""}, 2: {title:"",link:"",date,:"",from:""},... 
                },
                "engine2": {
                    answerBox:{title:"",link:"",date,:"",from:""}, 1: {title:"",link:"",date,:"",from:""}, 2: {title:"",link:"",date,:"",from:""},... 
                }
            },
            "商汤 股票 2024": {
                "engine1": {
                    answerBox:{title:"",link:"",date,:"",from:""}, 1: {title:"",link:"",date,:"",from:""}, 2: {title:"",link:"",date,:"",from:""},... 
                },
                "engine2": {
                    ### empty if engine 2 has an error
                }
            }
        }
        """
        # pprint(temp_results)
        query_with_link = {}
        fields_data = {}
        query_snippets = {}
        query_links = {}
        try:
            query_with_link, fields_data, query_snippets, query_links = (
                stage1_format2query_with_link(
                    temp_results,
                    cached_link_first=self.using_cached_link_first,
                    include_title=self.reranker_using_title,
                )
            )
        except BaseException as e:
            pass
        # logger.error(query_with_link)
        return query_with_link, fields_data, query_snippets, query_links, error_data

    async def stage2_3(
            self,
            ori_query: str,
            query_with_link: dict,
            query_snippets: dict,
            query_links: dict,
    ):
        logger.info("fetch start")
        fetch_start_time = time.perf_counter()

        error_data = None
        query_scrape_res = {}
        topk_query_with_link = {}
        res_query_with_link = {}

        try:
            query_scrape_res_task = asyncio.create_task(
                self.crawler.scrape(ori_query, query_links)
            )
            # limiter = CapacityLimiter(200)
            # topk_query_with_link, res_query_with_link = await to_thread.run_sync(
            #     self.topk_method.ranking, query_with_link, query_snippets, limiter=limiter
            # )
            topk_query_with_link, res_query_with_link = await run_in_threadpool(
                self.topk_method.ranking, query_with_link, query_snippets
            )
            logger.info(
                f"fetch rerank end-----cost time:{round(time.perf_counter() - fetch_start_time, 4)}----- link:{topk_query_with_link}")
            query_scrape_res, error_scrape_rate = await query_scrape_res_task
            logger.info(f"fetch crawler end-----cost time:{round(time.perf_counter() - fetch_start_time, 4)}")
        except BaseException as e:
            from src.traveler_exception import error_code_rerank_http_error
            error_data = ErrorDataModel(error_code=error_code_rerank_http_error, error_message=f"Fail to scrape web or rerank, e: {str(e)}")

        llm_main_ref = ""
        llm_rest_ref = ""
        try:
            llm_main_ref, llm_rest_ref = stage3_format_web_fetch(
                query_scrape_res,
                topk_query_with_link,
                res_query_with_link,
                truncate_length=self.truncate_length,
                rest_k=self.rest_k,
            )
        except BaseException as e:
            pass
        return llm_main_ref, llm_rest_ref, error_data

    async def stage4_llm_summary(
            self,
            query,
            reference_content_,
            rest_ref,
            request_id,
            request_data: TextModerationRequest,
            search=True,
            position="上海",
            multimodal="",
    ):
        if search:
            reference_content_ += rest_ref
            reference_content_ = reference_content_.replace("疫情", "")
            summary_user_prompt = travel_search_summary_user_prompt_zh.format(
                web_content=reference_content_,
                user_input=query,
                formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M"),
            )
            # logger.info(f"summary_user_prompt: {summary_user_prompt}")
            # logger.info(f"summary_user_prompt: {len(summary_user_prompt)}")
            summary_system_prompt = travel_search_summary_system_prompt_zh_v1.format(
                formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                position=position,
                multimodal=multimodal
            )
        else:
            summary_user_prompt = summary_user_prompt_zh.format(
                user_input=query,
                formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M"),
            )
            summary_system_prompt = summary_system_prompt_zh.format(
                formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

        messages = (
                [{"role": "system", "content": summary_system_prompt}]
                + self.public_memory.get_past_messages()
                + [{"role": "user", "content": summary_user_prompt}]
        )
        logger.debug(f"stage4_llm_summary messages:   {messages}")

        full_response = ""
        is_first_token = True
        try:
            async for chunk in self.summary_llm.yield_chat_from_db(
                    messages,
                    stream=True,
            ):

                full_response += chunk["content"]
                # send notification of when first token from llm appears
                if is_first_token:
                    mft = {
                        "data": "",
                        "type": "t6-mft",
                    }
                    yield mft

                if not self.detect:
                    yield chunk
                else:
                    ###################################### 输出接入nova检测敏感词#######################################
                    request_data.text = chunk["content"]
                    decision, tokens = await stream_output_moderate_text(
                        str(request_id), request_data
                    )
                    if decision == "BLOCK":
                        yield {"content": "。(尊敬的用户您好，让我们换个话题再聊聊吧～)"}
                        return
                    elif decision == "Further":
                        continue
                    elif decision == "PASS":
                        res = " ".join(tokens)
                        yield {"content": res}
                        logger.debug(res, end="")
                # 用于计算安全检测过程耗时
                if is_first_token:
                    is_first_token = False
                    sft = {
                        "data": "",
                        "type": "t7-sft",
                    }
                    yield sft
        except BaseException as e:
            from src.traveler_exception import error_code_chat_summary_http_error
            yield {
                "type": "error",
                "code": error_code_chat_summary_http_error,
                "data": "Chat summary call openai error",
            }
            ####################################### 输出接入nova检测敏感词#######################################

        if self.detect:
            ####################################### 输出接入nova检测敏感词,结束流式#######################################
            request_data.finish_detect = True
            decision, tokens = await stream_output_moderate_text(
                str(request_id), request_data
            )

            if decision == "BLOCK":
                yield {"content": "。(尊敬的用户您好，让我们换个话题再聊聊吧～)"}
                return
            # elif decision == "Further":
            # continue
            elif decision == "PASS":
                res = " ".join(tokens)
                yield {"content": res}
                logger.debug(res, end="")
            ###################################### 输出接入nova检测敏感词,结束流式#######################################

    async def stage5_follow_up_question(self, raw_query: str, tts_content: str) -> FollowUpQuestionResultModel:
        # memory = self.public_memory.get_past_messages()
        error_data = None
        query = raw_query + "\n" + tts_content + "\n"
        follow_up_prompt = follow_up_prompts.format(chat_his=query)
        # 没有system prompt
        messages = [{"role": "user", "content": follow_up_prompt}]
        # logger.info(f"messages:   {messages}")
        state5_result = None
        try:
            state5_result = await self.intent_llm.chat(
                messages,
                stream=False,
            )
        except BaseException as e:
            from src.traveler_exception import error_code_relative_http_error
            error_data = ErrorDataModel(error_code=error_code_relative_http_error,
                                        error_message=f"Chat follow up http error, e: {str(e)}")
        return FollowUpQuestionResultModel(result_str=state5_result, error_info=error_data)

    async def stage5_conclusion_title(self, raw_query: str, tts_content: str, poi_str: str) -> ConclusionTitleResultModel:
        # memory = self.public_memory.get_past_messages()
        query = raw_query + "\n" + tts_content + "\n"
        error_data = None
        conclusion_prompt = conclusion_prompts.format(chat_his=query, pois=poi_str)
        # 没有system prompt
        messages = [{"role": "user", "content": conclusion_prompt}]
        # logger.info(f"messages:   {messages}")
        state5_result = None
        try:
            state5_result = await self.summary_llm.chat(
                messages,
                stream=False,
            )
        except BaseException as e:
            from src.traveler_exception import error_code_conclusion_title_http_error
            error_data = ErrorDataModel(error_code=error_code_conclusion_title_http_error,
                                        error_message=f"Chat conclusion title http error, e: {str(e)}")
        return ConclusionTitleResultModel(result=state5_result, error_info=error_data)

    async def stage5_follow_up_question_http(self):
        # memory = self.summary_llm.memory.get_all_messages()
        query = (
                self.public_memory.get_past_messages()[-2]["content"]
                + "\n"
                + self.public_memory.get_past_messages()[-1]["content"]
                + "\n"
        )
        query = follow_up_prompts.format(chat_his=query)
        full_response = ""
        async for chunk in self.intent_llm.yield_chat(query):
            full_response += chunk["content"]
            yield chunk["content"]

    def convert_query_with_link_to_frontend_ref(self, data):
        results = []
        i = 1
        for key, articles in data.items():
            for _, article in articles.items():
                result = {
                    "index": i,
                    "title": article["title"],
                    "url": article["link"],
                    "url_source": "",
                    "icon": "",
                }
                if "wiki" in result["title"] or result["title"] == "":
                    continue
                results.append(result)
                i += 1
        return results

    @staticmethod
    async def pure_web_search(query: str, tools: List[WebSearch], search_nums=10):
        action_executor = ActionExecutor(tools)

        tasks = []
        tasks.append(action_executor("web_search", query, search_nums))
        results = await asyncio.gather(*tasks)

        temp_results = {}
        temp_results[query] = results[0]
        query_with_link, fields_data, query_snippets, query_links = (
            stage1_format2query_with_link(
                temp_results, cached_link_first=False, include_title=False
            )
        )
        return query_with_link

    async def chat(self, request: TravelChatQueryRequest, interceptor_queue: queue.Queue):
        try:
            query = request.query
            user_portrait = request.user_portrait
            position = request.position
            location = request.location
            multimodel = request.multimodal
            message_id = request.message_id
            request_id = request.request_id
            model_name = request.model_name
            resource = request.resource

            ####################################### 输入接入nova检测敏感词#######################################
            session_id = str(uuid.uuid4())
            user_id = "user_001"  # 车舱使用
            ext_info = {
                "model": model_name,
                "resource": resource,
                "user_id": user_id,
            }
            if self.detect:
                request_data = TextModerationRequest(
                    self.sensitive_config["ak"],
                    self.sensitive_config["sk"],
                    self.sensitive_config["app_id"],
                    user_id,
                    query,
                    "LLMPrompt",
                    session_id,
                    ext_info=ext_info,
                )
                result = await moderate_text_generic(
                    str(request_id), request_data, "输入"
                )
                if result:
                    response = {
                        "data": " 。(尊敬的用户您好，目前暂时无法回答这个话题哦～)",
                        "type": "message",
                        "messageId": message_id,
                    }
                    yield response
                    return
            # TODO: -------------------> 下述moderation请求，在stage4作校验？
            ####################################### 输出接入nova检测敏感词，入参定义#######################################
            text_id = str(uuid.uuid4())
            request_data = TextModerationRequest(
                self.sensitive_config["ak"],
                self.sensitive_config["sk"],
                self.sensitive_config["app_id"],
                user_id,
                "",
                "LLMStreamResponse",
                session_id,
                text_id=text_id,
                finish_detect=False,
                ext_info=ext_info,
            )
            ####################################### 输出接入nova检测敏感词，入参定义#######################################

            llm_responds = ""
            query_with_link = None
            from src.llm_content_helper import ContentHelper
            content_helper = ContentHelper()
            poi_list = None

            start_time = time.perf_counter()
            logger.info(f"pre_intent_res start")
            stage0_pre_intent_task = asyncio.create_task(self.stage0_pre_intent(query))
            pre_intent_res_model = await stage0_pre_intent_task
            pre_intent_res = pre_intent_res_model.result
            pre_intent_error = pre_intent_res_model.error_info
            logger.info(f"pre_intent_res end-----cost time: {round(time.perf_counter() - start_time,4)}-----result: {pre_intent_res}")

            if pre_intent_error:
                yield {
                    "type": "error",
                    "code": pre_intent_error.error_code,
                    "data": pre_intent_error.error_message,
                    "messageId": message_id,
                }

                return

            yield {
                "type": "pre_intent",
                "data": pre_intent_res,
                "messageId": message_id,
            }

            if pre_intent_res == "不相关":
                from src.traveler_exception import error_code_irrelative_intent
                err_code = {
                    "data": "Not travel relative conversation, should deny.",
                    "type": "error",
                    "code": error_code_irrelative_intent,
                    "messageId": message_id,
                }
                yield err_code

                response_data_list = ['这个', '问题', '有点', '超出', '我', '的', '知识', '范畴', '啦', '']
                for temp_data in response_data_list:
                    # 模拟异步延迟
                    await asyncio.sleep(0.1)
                    yield {"data": temp_data, "type": "message", "messageId": message_id}
                return

            intent_start_time = time.perf_counter()
            logger.info(f"intent rewrite start")
            stage0_intent_rewrite_task = asyncio.create_task(
                self.stage0_intent_rewrite(query, position, user_portrait, multimodal=multimodel))
            intent_json_output_model = await stage0_intent_rewrite_task
            intent_json_output = intent_json_output_model.result
            intent_json_output_error = intent_json_output_model.error_info
            logger.info(f"intent rewrite  end-----cost time:{round(time.perf_counter() - intent_start_time, 4)}s-----intent_json_output: {intent_json_output}")

            logger.info(
                "stage0_intent_rewrite_result FINISH",
            )

            if isinstance(intent_json_output_error, ErrorDataModel):
                yield {
                    "type": "error",
                    "code": intent_json_output_error.error_code,
                    "data": intent_json_output_error.error_message,
                    "messageId": message_id,
                }

            if len(intent_json_output[query]) > 3:
                intent_json_output[query] = intent_json_output[query][:2]

            code = 0
            search_list = intent_json_output.get(query, [])
            if not search_list or 0 >= len(search_list):
                from src.traveler_exception import error_code_rewrite_search_list_empty
                err_code = {
                    "data": "search_list(keywords) is null or empty",
                    "code" : error_code_rewrite_search_list_empty,
                    "type": "error",
                    "messageId": message_id,
                }
                yield err_code
                return

            keywords = {
                "data": search_list,
                "type": "t3-intent",
                "messageId": message_id,
            }
            yield keywords

            src_start_time = time.perf_counter()
            logger.info(f"src start")
            stage1_task = asyncio.create_task(self.stage1_web_search(search_list))
            (
                query_with_link,
                fields_data,
                query_snippets,
                query_links,
                src_error_data
            ) = await stage1_task
            logger.info(f"src end-----cost time: {round(time.perf_counter() - src_start_time,4)}s-----link: {query_with_link}")
            if all(not value for value in query_with_link.values()):
                logger.warning("所有搜索引擎都没搜到")
                from src.traveler_exception import error_code_src_search_empty
                err_code = {
                    "data": "There is nothing from web search.",
                    "code": error_code_src_search_empty,
                    "type": "error",
                    "messageId": message_id,
                }
                yield err_code

                return

                frontend_ref = self.convert_query_with_link_to_frontend_ref(
                    query_with_link
                )
                logger.debug(f"frontend_ref: {frontend_ref}")
                sources = {
                    "data": frontend_ref,
                    "type": "t4-src",
                    "messageId": message_id,
                }

                yield sources

                async for chunk in self.stage4_llm_summary(
                        query, "None", "None", request_id, request_data, search=False
                ):
                    llm_responds += chunk["content"]
                    result = {
                        "data": chunk["content"],
                        "type": "message",
                        "messageId": message_id,
                    }
                    if "usage" in chunk:
                        result["usage"] = chunk["usage"]
                    yield result

                result = {"type": "messageEnd", "messageId": message_id}
                yield result
                return

            frontend_ref = self.convert_query_with_link_to_frontend_ref(
                query_with_link
            )
            sources = {
                "data": frontend_ref,
                "type": "t4-src",
                "messageId": message_id,
            }

            yield sources
            llm_main_ref, llm_rest_ref, ll_ref_error_data = await self.stage2_3(
                query, query_with_link, query_snippets, query_links
            )
            if isinstance(ll_ref_error_data, ErrorDataModel):
                yield {
                    "type": "error",
                    "code": ll_ref_error_data.error_code,
                    "data": ll_ref_error_data.error_message,
                    "messageId": message_id,
                }
            llm_whole_ref = llm_main_ref + llm_rest_ref
            fetch = {
                "data": llm_whole_ref,
                "type": "t5-fetch",
                "messageId": message_id,
            }
            yield fetch

            logger.info("llm_summary start")
            llm_summary_start = time.perf_counter()
            llm_summary_fist_word_time = 0
            llm_summary_fist_word_time_detect = 0
            async for chunk in self.stage4_llm_summary(
                    query,
                    llm_whole_ref,
                    llm_rest_ref,
                    request_id,
                    request_data,
                    search=True,
                    position=position,
                    multimodal=multimodel,
            ):
                try:
                    queue_item = interceptor_queue.get_nowait()
                    if isinstance(queue_item, dict):
                        yield queue_item
                    elif isinstance(queue_item, ErrorDataModel):
                        yield  {
                            # "data": "Fail to get gaode info of pois.",
                            "code": queue_item.error_code,
                            "data": queue_item.error_message,
                            "type": "error",
                            "messageId": message_id
                        }
                except queue.Empty:
                    pass

                if "type" in chunk:
                    if "t6-mft" == chunk["type"]:
                        llm_summary_fist_word_time = round(time.perf_counter() - llm_summary_start, 4)
                    elif "t7-sft" == chunk["type"]:
                        llm_summary_fist_word_time_detect = round(time.perf_counter() - llm_summary_start, 4)

                if "content" not in chunk:
                    chunk['messageId'] = message_id
                    yield chunk
                    continue

                llm_responds += chunk["content"]
                tts_content = content_helper.append_content(chunk["content"])
                if not poi_list:
                    poi_list = content_helper.get_poi_list()
                    if poi_list:
                        poi_list_result = {
                            "data": poi_list,
                            "type": "poi_list",
                            "messageId": message_id,
                        }
                        yield poi_list_result


                result = None
                if tts_content:

                    result = {
                        "data": tts_content,
                        "type": "message",
                        "messageId": message_id,
                    }
                if result:
                    if chunk.get("code", 0) != 0:
                        error_msg = f"Chat summary error, code: {chunk['code']}"
                        from src.traveler_exception import error_code_chat_summary_llm_error
                        yield {
                            "type": "error",
                            "code": error_code_chat_summary_llm_error,
                            "data": error_msg,
                            "messageId": message_id,

                        }
                        result["code"] = chunk["code"]
                        result["type"] = "error"
                        code = chunk["code"]
                    if "usage" in chunk:
                        result["usage"] = chunk["usage"]
                    yield result
            logger.info(f"llm_summary end-----detect:{self.detect}-----first token time: {llm_summary_fist_word_time if not self.detect else llm_summary_fist_word_time_detect}-----total time: {round(time.perf_counter() - llm_summary_start, 4)}")
            detail_info = content_helper.poi_detail_dict()

            detail_info_str = ""
            if detail_info:
                detail_info_result = {
                    "data": detail_info,
                    "type": "detail_info",
                    "messageId": message_id,
                }
                yield detail_info_result
                detail_info_str = json.dumps(detail_info, ensure_ascii=False)
            else:
                from src.traveler_exception import error_code_chat_summary_format_error
                yield {
                    "data": "Fail to get detail info of pois.",
                    "code": error_code_chat_summary_format_error,
                    "type": "error",
                    "messageId": message_id
                }

            logger.info("conclusion start")
            conclusion_title_model = await self.stage5_conclusion_title(raw_query=query, tts_content=content_helper.get_tts_content_after_end(), poi_str=detail_info_str)
            if conclusion_title_model.error_info:
                yield {
                    "type": "error",
                    "code": conclusion_title_model.error_info.error_code,
                    "data": conclusion_title_model.error_info.error_message,
                    "messageId": message_id,

                }
            conclusion_title_str = conclusion_title_model.result
            logger.info(f"conclusion end, result: {conclusion_title_str}")

            logger.info("follow_up_question start")
            follow_up_question_model = await self.stage5_follow_up_question(raw_query=query, tts_content=content_helper.get_tts_content_after_end())
            if follow_up_question_model.error_info:
                yield {
                    "type": "error",
                    "code": follow_up_question_model.error_info.error_code,
                    "data": follow_up_question_model.error_info.error_message,
                    "messageId": message_id,
                }
            follow_up_question_str = follow_up_question_model.result_str
            logger.info(f"follow_up_question end, result: {follow_up_question_str}")
            try:
                follow_up_questions = json.loads(follow_up_question_str)
                if isinstance(follow_up_questions, list):
                    if 3 == len(follow_up_questions):
                        yield {
                            "data": follow_up_questions,
                            "conclusion_title": conclusion_title_str,
                            "type": "follow_up",
                            "messageId": message_id,
                        }
                    else:
                        follow_up_questions_list = []
                        for question_item in follow_up_questions:
                            if "|" in question_item:
                                split_question_item = question_item.split("|")
                                follow_up_questions_list += split_question_item
                        if 3 < len(follow_up_questions):
                            follow_up_questions_list = follow_up_questions_list[0:2]
                        yield {
                            "data": follow_up_questions_list,
                            "conclusion_title": conclusion_title_str,
                            "type": "follow_up",
                            "messageId": message_id,
                        }
            except Exception as e:
                try:
                    follow_up_questions = follow_up_question_str.split("|")
                    if 3 < len(follow_up_questions):
                        follow_up_questions = follow_up_questions[0:2]
                    if isinstance(follow_up_questions, list):
                        yield {
                            "data": follow_up_questions,
                            "conclusion_title": conclusion_title_str,
                            "type": "follow_up",
                            "messageId": message_id,
                        }
                except BaseException as e:
                    from src.traveler_exception import error_code_relative_format_error
                    if conclusion_title_str:
                        yield {
                            "conclusion_title": conclusion_title_str,
                            "type": "follow_up",
                            "messageId": message_id,
                        }
                    yield {
                        "type": "error",
                        "code": error_code_relative_format_error,
                        "data": f"follow up relative format error, raw data: {follow_up_question_str}",
                        "messageId": message_id,
                    }
                    pass

            result = {"type": "messageEnd", "messageId": message_id, "code": code}
            yield result

        except openai.BadRequestError as e:
            err_code = e.code
            message = e.response.json().get('error', {'message': ""}).get('message')
            result = {"type": "messageEnd", "messageId": message_id, "data": message, "code": err_code}
            yield result

        except Exception as e:
            logger.info(e)
            import traceback

            traceback.print_exc()
