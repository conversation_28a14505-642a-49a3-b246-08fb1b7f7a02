import uuid
from pydantic import BaseModel, ConfigDict, model_validator
from typing import Optional, Annotated, Self
from annotated_types import Len


class Message(BaseModel):
    role: str
    content: str


class WebSearchReq(BaseModel):
    query: str
    history: Optional[list[Message]] = None
    mode: str = "turbo"
    engine: Optional[str] = "hybrid"
    k: int = 10
    stream: bool = False
    detect: bool = False
    just_answer: bool = False


class WebSearchReqV2(BaseModel):
    model_config = ConfigDict(protected_namespaces=())
    query: Optional[str] = None
    history: Optional[list[Message]] = None
    k: int = 10
    engine: str = ""
    rerank_method: Optional[str] = "reranker"
    stream: bool = False
    detect: bool = False
    chat_id: Optional[uuid.UUID] = None
    messages: Optional[list[Message]] = None
    use_search_cache: Optional[bool] = False

    @model_validator(mode='after')
    def either_query_or_messages_list(self) -> Self:
        if self.query is None and self.messages is None:
            raise ValueError('query and messages cannot all be None')
        if self.messages is not None:
            if self.messages[-1].role != "user":
                raise ValueError('The role of the last message must be "user"')
        return self


class UserMemoryReq(BaseModel):
    car_id: str
    user_id: str
    category: list[str] = []

class LocationReq(BaseModel):
    lat: str
    lon: str

    def build_coordinate_text(self) -> Optional[str]:
        try:
            return format(float(self.lon), ".4f") + "," + format(float(self.lat), ".4f")
        except BaseException as e:
            print(e)
            return None

    @model_validator(mode='after')
    def both_lat_and_lon_valid(self) -> Self:
        if self.build_coordinate_text():
            return self
        else:
            raise ValueError(f'lat: {self.lat} lon: {self.lon} is not valid' )
            

class WebSearchReqGuangqiDifyV1(WebSearchReqV2):
    user_info: Optional[UserMemoryReq] = None
    location: Optional[LocationReq] = None
    surrounding: Optional[str] = None

