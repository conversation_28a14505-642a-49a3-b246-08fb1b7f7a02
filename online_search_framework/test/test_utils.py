#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Online Search Framework 测试工具函数
提供测试过程中的通用功能和工具函数
"""

import jwt
import time
import json
import requests
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta, timezone


class JWTTokenManager:
    """JWT令牌管理器"""
    
    def __init__(self, ak: str = "146049b1-0a95-49a2-8856-5e5e35f0f9a6", 
                 sk: str = "b8bb9b60-9b3d-4e68-be09-f1e6816794d4"):
        self.ak = ak
        self.sk = sk
        self._token = None
        self._token_expiry = None
    
    def get_token(self) -> str:
        """获取有效的JWT令牌"""
        if self._token is None or self._is_token_expired():
            self._token = self._create_access_token()
            self._token_expiry = datetime.now(timezone.utc) + timedelta(days=6)  # 提前1天过期
        return self._token
    
    def _is_token_expired(self) -> bool:
        """检查令牌是否过期"""
        if self._token_expiry is None:
            return True
        return datetime.now(timezone.utc) >= self._token_expiry
    
    def _create_access_token(self) -> str:
        """创建JWT访问令牌"""
        expire = datetime.now(timezone.utc) + timedelta(days=7)
        nbf = datetime.now(timezone.utc) - timedelta(minutes=1)
        to_encode = {"iss": self.ak, "exp": expire, "nbf": nbf}
        encoded_jwt = jwt.encode(to_encode, self.sk, algorithm="HS256")
        return encoded_jwt


class TestResultCollector:
    """测试结果收集器"""
    
    def __init__(self):
        self.results = []
        self.start_time = time.time()
    
    def add_result(self, test_name: str, success: bool, message: str, 
                   duration: float = 0, details: Optional[Dict[str, Any]] = None):
        """添加测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "duration": duration,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "details": details or {}
        }
        self.results.append(result)
    
    def get_summary(self) -> Dict[str, Any]:
        """获取测试总结"""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r["success"])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        total_duration = time.time() - self.start_time
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": success_rate,
            "total_duration": total_duration,
            "overall_success": failed_tests == 0
        }
    
    def print_summary(self):
        """打印测试总结"""
        summary = self.get_summary()
        
        print("📊 测试总结:")
        print(f"   总测试数: {summary['total_tests']}")
        print(f"   通过测试: {summary['passed_tests']}")
        print(f"   失败测试: {summary['failed_tests']}")
        print(f"   成功率: {summary['success_rate']:.1f}%")
        print(f"   总耗时: {summary['total_duration']:.2f}s")
        
        if summary['overall_success']:
            print("🎉 所有测试通过！")
        elif summary['success_rate'] >= 75:
            print("✅ 大部分测试通过，基本正常")
        else:
            print("⚠️  多项测试失败，请检查服务状态")


class APIClient:
    """API客户端封装"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        self.base_url = base_url
        self.health_url = f"{base_url}/cabin/web_search/health"
        self.search_url = f"{base_url}/cabin/web_search/v2"
        self.token_manager = JWTTokenManager()
    
    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token_manager.get_token()}"
        }
    
    def health_check(self, timeout: int = 10) -> Dict[str, Any]:
        """健康检查"""
        try:
            response = requests.get(self.health_url, timeout=timeout)
            return {
                "success": response.status_code == 200,
                "status_code": response.status_code,
                "data": response.json() if response.status_code == 200 else None,
                "error": None
            }
        except Exception as e:
            return {
                "success": False,
                "status_code": None,
                "data": None,
                "error": str(e)
            }
    
    def search(self, query: str, engine: str = "bing", stream: bool = False, 
               k: int = 5, detect: bool = True, timeout: int = 60) -> Dict[str, Any]:
        """搜索请求"""
        payload = {
            "engine": engine,
            "query": query,
            "stream": stream,
            "detect": detect,
            "k": k
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                self.search_url,
                headers=self.get_headers(),
                json=payload,
                timeout=timeout,
                stream=stream
            )
            duration = time.time() - start_time
            
            if stream:
                # 处理流式响应
                chunks = []
                for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                    if chunk:
                        chunks.append(chunk)
                        # 限制读取时间
                        if time.time() - start_time > timeout:
                            break
                
                return {
                    "success": response.status_code == 200,
                    "status_code": response.status_code,
                    "data": {"chunks": chunks, "chunk_count": len(chunks)},
                    "duration": duration,
                    "error": None
                }
            else:
                # 处理普通响应
                data = None
                if response.status_code == 200:
                    try:
                        data = response.json()
                    except json.JSONDecodeError:
                        pass
                
                return {
                    "success": response.status_code == 200,
                    "status_code": response.status_code,
                    "data": data,
                    "duration": duration,
                    "error": None
                }
                
        except Exception as e:
            duration = time.time() - start_time
            return {
                "success": False,
                "status_code": None,
                "data": None,
                "duration": duration,
                "error": str(e)
            }


class TestDataGenerator:
    """测试数据生成器"""
    
    @staticmethod
    def get_basic_queries() -> List[str]:
        """获取基础测试查询"""
        return [
            "北京今天的天气",
            "上海有什么好吃的餐厅",
            "深圳科技园附近的酒店",
            "广州白云机场到市区怎么走",
            "杭州西湖景区门票价格"
        ]
    
    @staticmethod
    def get_special_queries() -> List[str]:
        """获取特殊字符测试查询"""
        return [
            "测试中文查询",
            "Test English Query",
            "测试 Mixed 语言 Query",
            "特殊符号!@#$%^&*()",
            "数字123456789",
            "emoji测试🔍🌟💡",
            "HTML标签<script>alert('test')</script>",
            "JSON格式{\"test\": \"value\"}"
        ]
    
    @staticmethod
    def get_long_queries() -> List[str]:
        """获取长查询测试数据"""
        base_query = "这是一个测试查询，用于验证系统对长查询的处理能力。"
        return [
            base_query * 5,    # 约150字符
            base_query * 10,   # 约300字符
            base_query * 20,   # 约600字符
            base_query * 50    # 约1500字符
        ]
    
    @staticmethod
    def get_boundary_payloads() -> List[Dict[str, Any]]:
        """获取边界条件测试载荷"""
        return [
            {"engine": "bing", "query": "测试", "k": 1, "stream": False, "detect": True},
            {"engine": "bing", "query": "测试", "k": 50, "stream": False, "detect": True},
            {"engine": "bing", "query": "测试", "k": 0, "stream": False, "detect": True},
            {"engine": "bing", "query": "a", "k": 5, "stream": False, "detect": True},
            {"engine": "bing", "query": "", "k": 5, "stream": False, "detect": True}
        ]
    
    @staticmethod
    def get_invalid_payloads() -> List[Dict[str, Any]]:
        """获取无效参数测试载荷"""
        return [
            {"engine": "invalid_engine", "query": "测试", "k": 5, "stream": False, "detect": True},
            {"k": 5, "stream": False, "detect": True},  # 缺少必需参数
            {"engine": "bing", "query": 123, "k": "invalid", "stream": "not_bool", "detect": True},
            {},  # 空载荷
        ]


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        self.response_times = []
        self.timing_data = []
    
    def add_response_time(self, duration: float):
        """添加响应时间"""
        self.response_times.append(duration)
    
    def add_timing_data(self, timing_info: Dict[str, float]):
        """添加时间详情数据"""
        self.timing_data.append(timing_info)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取性能统计"""
        if not self.response_times:
            return {}
        
        from statistics import mean, median
        
        stats = {
            "count": len(self.response_times),
            "mean": mean(self.response_times),
            "median": median(self.response_times),
            "min": min(self.response_times),
            "max": max(self.response_times)
        }
        
        # 分析模块耗时
        if self.timing_data:
            module_stats = {}
            for timing_info in self.timing_data:
                for field, duration in timing_info.items():
                    if field not in module_stats:
                        module_stats[field] = []
                    if duration > 0:
                        module_stats[field].append(duration)
            
            for field, durations in module_stats.items():
                if durations:
                    module_stats[field] = {
                        "mean": mean(durations),
                        "count": len(durations)
                    }
            
            stats["modules"] = module_stats
        
        return stats
    
    def print_statistics(self):
        """打印性能统计"""
        stats = self.get_statistics()
        if not stats:
            print("   无性能数据")
            return
        
        print(f"   📊 性能统计:")
        print(f"   请求数量: {stats['count']}")
        print(f"   平均响应时间: {stats['mean']:.2f}s")
        print(f"   中位数响应时间: {stats['median']:.2f}s")
        print(f"   最快响应时间: {stats['min']:.2f}s")
        print(f"   最慢响应时间: {stats['max']:.2f}s")
        
        if "modules" in stats:
            print(f"   模块耗时分析:")
            sorted_modules = sorted(stats["modules"].items(),
                                  key=lambda x: x[1]["mean"], reverse=True)
            for field, module_stat in sorted_modules:
                print(f"     {field:15s}: {module_stat['mean']:6.2f}s (出现{module_stat['count']}次)")


def extract_timing_info(response_data: Dict[str, Any]) -> Dict[str, float]:
    """从响应数据中提取时间信息"""
    timing_info = {}
    
    if isinstance(response_data, dict):
        time_cost = response_data.get("time_cost", {})
        if isinstance(time_cost, dict):
            time_fields = ["keywords", "sources", "fetch", "TTFT", "total_time"]
            
            for field in time_fields:
                if field in time_cost:
                    try:
                        timing_info[field] = float(time_cost[field])
                    except (ValueError, TypeError):
                        pass
    
    return timing_info


def wait_for_service(base_url: str, max_wait_time: int = 120, 
                    check_interval: int = 5) -> bool:
    """等待服务就绪"""
    client = APIClient(base_url)
    wait_time = 0
    
    print(f"⏳ 等待服务就绪 (最大等待时间: {max_wait_time}s)...")
    
    while wait_time < max_wait_time:
        result = client.health_check(timeout=10)
        if result["success"]:
            print("✅ 服务已就绪")
            return True
        
        print(f"   等待中... ({wait_time}/{max_wait_time}s)")
        time.sleep(check_interval)
        wait_time += check_interval
    
    print("❌ 服务等待超时")
    return False


if __name__ == "__main__":
    # 测试工具函数
    print("🧪 测试工具函数验证")
    
    # 测试JWT令牌管理器
    token_manager = JWTTokenManager()
    token = token_manager.get_token()
    print(f"JWT令牌生成成功: {token[:50]}...")
    
    # 测试API客户端
    client = APIClient()
    health_result = client.health_check()
    print(f"健康检查结果: {health_result}")
    
    # 测试数据生成器
    queries = TestDataGenerator.get_basic_queries()
    print(f"基础查询数量: {len(queries)}")
    
    print("✅ 工具函数验证完成")
