#!/bin/bash

# 设置变量
API_URL="http://127.0.0.1:8080/gac/travel-companion/v2/chat"  # 替换为实际的API地址
QUERY="附近的便利店"               # 可替换为实际查询内容

# 构建 JSON payload
PAYLOAD='{
  "location": {
    "lat": "23.02965",
    "lon": "113.49027"
  },
  "model_name": "SenseAutoChat-30B",
  "rewrite_model_name": "POI-rewrite-7B",
  "messages": [
    {
      "role": "user",
      "content": "'"$QUERY"'"
    }
  ],
  "is_stream": true,
  "user_info": {
    "car_id": "demoCar82951",
    "user_id": "2",
    "category": [
      "natural_landscape_preference",
      "human_landscape_preference",
      "entertainment_landscape_preference",
      "travel_activity"
    ]
  }
}'

# 使用 curl 发送请求
curl -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d "$PAYLOAD"