settings:
  version: 1.1.19
  debug: $DEBUG_MODE|false
  port: 8080


llm_server:
  api_key: $SENSEAUTO_KEY|1s3963nw8802M4O55yMuU6x37tOYQ682
  base_url: $SENSEAUTO_URL|http://************:8099/v1
  model_name: $SENSEAUTO_MODEL_NAME|SenseAuto-Chat
  temperature: 0.1
  top_p: 0.5
  max_tokens: 4096

sport_chat:
#  url: https://sit-platform.senseauto.com/gac/web_search/v1/sport_chat
  url: http://gac-online-search-service:8080/gac/web_search/v1/sport_chat
  client-id: $CLIENT_ID|1s3963nw8802M4O55yMuU6x37tOYQ682

user_memory:
  url: $MEMORY_URL|https://sit-platform.senseauto.com/gac-agent/v1/memory/search

migu_source_data:
  host: $MIGU_CACHE_HOST|"https://sit-platform.senseauto.com"
  url_competition: "/gac-migu/v2/get_competitions"
  url_matches: "/gac-migu/v1/query_matches"
  url_score: "/gac-migu/v1/get_score"
  client_id: $MIGU_CACHE_CLIENT_ID|"1s3963nw8802M4O55yMuU6x37tOYQ682"


# 敏感词接口
sensitive:
  ak: "516D565585C94D1196B56E82FF8A2AF6"
  sk: "60219DC604C84BA782EEA9EBFBD50754"
  app_id: "536381751228949387"

# gaode查询相关内容，主要用在坐标转地址的功能上
gaode:
  api_key: $GAODE_API_KEY|"727214f691c1b3cc2c35d197679be682"
  regeo:
    url: $GAODE_URL_REGEO|https://restapi.amap.com/v3/geocode/regeo


search_engine:
  serper:
    key: "70385833ec2595a8f5aab289320c564e3ed69a8a" #web部署key 5w quota
  bing:
    url: "https://searchapi.xiaosuai.com/search/QEIjmDqtHbJEfmYc/smart"
    key: $BING_KEY|"fake"
  tencent:
    ak: $TENCENT_AK|"AKIDpaBZljYDfu9MWksreDakjKm2gBTkpy1v"
    sk: $TENCENT_SK|"nNkEZidMrAIIKEvHSSGqERSHwQjqlXCj"

scrape:
  engine-in-use: go-readability
  go-readability:
    url: $SCRAPE_URL|http://cabin-osf-go-readability.autocloud-platform.svc:8780/parse
  python-document:
    url: ""  # 非http服务

llm_server_set:
  senseauto:
    api_key: $SENSEAUTO_KEY|1s3963nw8802M4O55yMuU6x37tOYQ682
    base_url: $SENSEAUTO_URL|http://************:8099/v1
    model_name: $SENSEAUTO_MODEL_NAME|SenseAuto-Chat
    temperature: 0.1
    top_p: 0.5
    max_tokens: 4096

  nova_stage:
    ak: "2Xn0OxgnHQhrp70vqmQAiYsrLTx"
    sk: "mhwBfoGigMkWFdb0OSbfMRsKi6vUa8bG"
    model_name: "SenseAuto-Chat-stage"
    base_url: "https://api.stage.sensenova.cn/compatible-mode/v1/"  # nova stage 环境
    temperature: 0.1
    top_p: 0.5
    max_tokens: 4096

  nova_prod:
    ak: "2V3uatcedTJPhW9srmCt5TsCfmp"
    sk: "XzoVqkGWjVONZx5G8jWfVPluXuRkuGlI"
    model_name: "SenseAuto-Chat"
    base_url: "https://api.sensenova.cn/compatible-mode/v1/"  # nova prod 环境
    temperature: 0.1
    top_p: 0.5
    max_tokens: 4096

selected_llm_server: $SELECTED_MODEL|senseauto


rerank:
  api_key: cc1781d49cd6e0284606ca47ac0d4bcb
  base_url: $RERANK_URL|https://**************:8077/rerank #8k

# 默认也复用selected_llm_server，加一些override的参数
rewrite:
    temperature: 0.01
    base_url: $REWRITE_URL|"https://**************:8090/v1"
    model_name: $REWRITE_MODEL_NAME|"/mnt/afs/wangyuhang/model/websearch_7b_1119v1/hf_model"
    api_key: d941dcfdaaeb637267fc06bda9ae6770
    type: "7b"
    max_tokens: 128


redis:
  host: $REDIS_HOST|lg.paas.sensetime.com
  port: $REDIS_PORT|32680
  password: $REDIS_PASSWORD|0YpvS1WKs8
  username: $REDIS_USERNAME|""