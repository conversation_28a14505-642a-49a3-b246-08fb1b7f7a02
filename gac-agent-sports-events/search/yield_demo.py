import asyncio, uuid, time, <PERSON>ai, json
from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from typing import Union, Dict, List
from loguru import logger
from starlette.concurrency import run_in_threadpool

from search.nova.security_policy import (
    TextModerationRequest,
    stream_output_moderate_text,
    moderate_text_generic,
)
from prompts.prompt_follow_up import follow_up_prompts
from prompts.prompt_search_summary import (
    search_summary_system_prompt_zh_v1,
    summary_system_prompt_zh,
    search_summary_user_prompt_zh,
    summary_user_prompt_zh,
)
from search.actions.other_actions.topk_base import TopkBase
from search.actions.tool_actions.action_excutor import ActionExecutor
from search.actions.tool_actions.web_search import WebSearch
from search.llms.openai_SDK import OpenAI_LLM
from search.memory.memory_base import MemoryBase
from search.search_utils import (
    stage0_parse_intent,
    stage1_format2query_with_link,
    stage3_format_web_fetch,
    stage3_format_rest_query_with_link,
)
from search.web_content_scrape import <PERSON>S<PERSON>rape, WebScrapePy
from constance.constance_value import DEFAULT_LOCATION
from schemas.models import ErrorDataModel, RewriteResultModel, FollowUpQuestionResultModel, ConclusionTitleResultModel


@dataclass
class WebChatQueryRequest:
    query: str
    message_id: str
    start_time: float = time.perf_counter()
    user_portrait: Optional[str] = None
    position: Optional[str] = DEFAULT_LOCATION
    request_id: Optional[str] = None
    model_name: Optional[str] = None
    resource: str = "default"


class WebSearchAgent:
    def __init__(
            self,
            intent_llm: OpenAI_LLM,
            summary_llm: OpenAI_LLM,
            topk_method: Union[TopkBase, None],
            action_executor: ActionExecutor,
            public_memory: MemoryBase,
            crawler: Union[WebScrape, WebScrapePy],
            router_system_prompt: str,
            router_user_prompt: str,
            dojo: None = None,
            detect: bool = False,
            sensitive_config: Optional[Dict[str, str]] = None,
            search_nums: int = 10,
            rest_k: int = 5,
            using_cached_link_first: bool = False,
            reranker_using_title: bool = False,
            truncate_length: int = 1500,
    ) -> None:
        self.action_executor = action_executor
        self.intent_llm = intent_llm
        self.summary_llm = summary_llm
        self.pre_intent_llm = summary_llm
        self.topk_method = topk_method
        self.crawler = crawler
        self.public_memory = public_memory
        self.detect = detect
        self.dojo = dojo
        if sensitive_config is None:
            self.sensitive_config = {}
        else:
            self.sensitive_config = sensitive_config
        self.rest_k = rest_k
        self.search_nums = search_nums
        self.using_cached_link_first = using_cached_link_first
        self.reranker_using_title = reranker_using_title
        self.truncate_length = truncate_length
        self.router_system_prompt = router_system_prompt
        self.router_user_prompt = router_user_prompt

    async def stage0_intent_rewrite(self, query, position="北京", user_portrait=None, multimodal="") -> RewriteResultModel:
        input_query = self.router_user_prompt.format(position=position, user_input=query, user_portrait=user_portrait)

        if multimodal != "":
            router_system_prompt = self.router_system_prompt.format(
                position=position, formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"), multimodal=multimodal
            )
        else:
            router_system_prompt = self.router_system_prompt.format(
                position=position, formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )
        # logger.error(self.public_memory)
        messages = (
                [{"role": "system", "content": router_system_prompt}]
                + self.public_memory.get_past_messages()
                + [{"role": "user", "content": input_query}]
        )
        error_data = None
        intent_return = "null"
        try:
            intent_return = await self.intent_llm.chat(
                messages,
                stream=False,
            )
        except BaseException as e:
            from constance.constance_value import error_code_rewrite_http_error
            error_data = ErrorDataModel(error_code=error_code_rewrite_http_error, error_message=f"Fail to call rewrite model, e: {str(e)}")

        intent_json_output = stage0_parse_intent(query, intent_return)
        return RewriteResultModel(result=intent_json_output, error_info=error_data)

    async def stage1_web_search(self, search_queries):
        """
        Perform asynchronous web searches for a list of queries, USING MULTI search engine if theres more than one.

        Args:
        - search_queries: A list of query strings. Each string represents a search query to be executed.

        e.g.
        search_queries: [商汤 股票 价格，商汤 股票 2024]

        Returns:
        - A dictionary where the keys are the original search queries and the values are the corresponding search results.

        self.action_executor("web_search", single_query) searchs each query using multiple search engine (if), see .
        """
        error_data = None
        temp_results = {}
        try:
            results = await asyncio.gather(
                *(
                    self.action_executor("web_search", query, self.search_nums)
                    for query in search_queries
                )
            )

            temp_results = {query: result for query, result in zip(search_queries, results)}
        except BaseException as e:
            from constance.constance_value import error_code_src_search_http_error
            error_data = ErrorDataModel(error_code=error_code_src_search_http_error, error_message=f"Fail to search on web, e: {str(e)}")
        """
        temp_results:
        {
            "商汤 股票 价格": {
                "engine1": {
                    answerBox:{title:"",link:"",date,:"",from:""}, 1: {title:"",link:"",date,:"",from:""}, 2: {title:"",link:"",date,:"",from:""},... 
                },
                "engine2": {
                    answerBox:{title:"",link:"",date,:"",from:""}, 1: {title:"",link:"",date,:"",from:""}, 2: {title:"",link:"",date,:"",from:""},... 
                }
            },
            "商汤 股票 2024": {
                "engine1": {
                    answerBox:{title:"",link:"",date,:"",from:""}, 1: {title:"",link:"",date,:"",from:""}, 2: {title:"",link:"",date,:"",from:""},... 
                },
                "engine2": {
                    ### empty if engine 2 has an error
                }
            }
        }
        """
        # pprint(temp_results)
        query_with_link = {}
        fields_data = {}
        query_snippets = {}
        query_links = {}
        try:
            query_with_link, fields_data, query_snippets, query_links = (
                stage1_format2query_with_link(
                    temp_results,
                    cached_link_first=self.using_cached_link_first,
                    include_title=self.reranker_using_title,
                )
            )
        except BaseException as e:
            from constance.constance_value import error_code_src_search_return_error
            error_data = ErrorDataModel(error_code=error_code_src_search_return_error,
                                        error_message=f"Fail to format search result, e: {str(e)}")
        # logger.error(query_with_link)
        return query_with_link, fields_data, query_snippets, query_links, error_data

    async def stage2_3(
            self,
            ori_query: str,
            query_with_link: dict,
            query_snippets: dict,
            query_links: dict,
    ):
        error_data = None
        query_scrape_res = {}
        topk_query_with_link = {}
        res_query_with_link = {}
        try:
            query_scrape_res_task = asyncio.create_task(
                self.crawler.scrape(ori_query, query_links)
            )
            # limiter = CapacityLimiter(200)
            # topk_query_with_link, res_query_with_link = await to_thread.run_sync(
            #     self.topk_method.ranking, query_with_link, query_snippets, limiter=limiter
            # )
            topk_query_with_link, res_query_with_link = await run_in_threadpool(
                self.topk_method.ranking, query_with_link, query_snippets
            )
            query_scrape_res, error_scrape_rate = await query_scrape_res_task
        except BaseException as e:
            from constance.constance_value import error_code_rerank_http_error
            error_data = ErrorDataModel(error_code=error_code_rerank_http_error, error_message=f"Fail to scrape web or rerank, e: {str(e)}")

        llm_main_ref = ""
        llm_rest_ref = ""
        try:
            llm_main_ref, llm_rest_ref = stage3_format_web_fetch(
                query_scrape_res,
                topk_query_with_link,
                res_query_with_link,
                truncate_length=self.truncate_length,
                rest_k=self.rest_k,
            )
        except BaseException as e:
            from constance.constance_value import error_code_rerank_return_error
            error_data = ErrorDataModel(error_code=error_code_rerank_return_error,
                                        error_message=f"Fail to scrape web or rerank, e: {str(e)}")
        return llm_main_ref, llm_rest_ref, error_data

    async def stage4_llm_summary(
            self,
            query,
            reference_content_,
            rest_ref,
            request_id,
            request_data: TextModerationRequest,
            pre_text="",
            search=True,
            position=DEFAULT_LOCATION,
            user_portrait=None,
    ):
        # if pre_text == "" or pre_text is None:

        if search:
            reference_content_ += rest_ref
            reference_content_ = reference_content_.replace("疫情", "")

            summary_user_prompt = search_summary_user_prompt_zh.format(
                user_portrait=user_portrait,
                web_content=reference_content_,
                user_input=query,
                formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M"),
            )
            summary_system_prompt = search_summary_system_prompt_zh_v1.format(
                formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                position=position
            )

        else:
            summary_user_prompt = summary_user_prompt_zh.format(
                user_input=query,
                formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M"),
            )
            summary_system_prompt = summary_system_prompt_zh.format(
                formatted_date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

        messages = (
                [{"role": "system", "content": summary_system_prompt}]
                + self.public_memory.get_past_messages()
                + [{"role": "user", "content": summary_user_prompt}]
        )
        logger.debug(f"stage4_llm_summary messages:   {messages}")

        full_response = ""
        is_first_token = True
        try:
            async for chunk in self.summary_llm.yield_chat_from_db(
                    messages,
                    stream=True,
            ):
                full_response += chunk["content"]
                # send notification of when first token from llm appears
                if is_first_token:
                    mft = {
                        "data": "",
                        "type": "mft",
                    }
                    yield mft

                if not self.detect:
                    yield chunk
                else:
                    ###################################### 输出接入nova检测敏感词#######################################
                    request_data.text = chunk["content"]
                    decision, tokens = await stream_output_moderate_text(
                        str(request_id), request_data
                    )
                    if decision == "BLOCK":
                        yield {"content": "。(尊敬的用户您好，让我们换个话题再聊聊吧～)"}
                        # from constance.constance_value import error_code_sensitive_answer
                        # yield {
                        #     "data": "The answer contains something sensitive.",
                        #     "type": "error",
                        #     "code": error_code_sensitive_answer,
                        # }
                        return
                    elif decision == "Further":
                        continue
                    elif decision == "PASS":
                        res = " ".join(tokens)
                        yield {"content": res}
                        logger.debug(res, end="")
                # 用于计算安全检测过程耗时
                if is_first_token:
                    is_first_token = False
                    sft = {
                        "data": "",
                        "type": "sft",
                    }
                    yield sft
        except BaseException as e:
            from constance.constance_value import error_code_chat_summary_http_error
            yield {
                "type": "error",
                "code": error_code_chat_summary_http_error,
                "data": "Chat summary call openai error",
            }
            ####################################### 输出接入nova检测敏感词#######################################

        if self.detect:
            ####################################### 输出接入nova检测敏感词,结束流式#######################################
            request_data.finish_detect = True
            decision, tokens = await stream_output_moderate_text(
                str(request_id), request_data
            )

            if decision == "BLOCK":
                yield {"content": "。(尊敬的用户您好，让我们换个话题再聊聊吧～)"}
                # from constance.constance_value import error_code_sensitive_answer
                # yield {
                #         "data": "The answer contains something sensitive.",
                #         "type": "error",
                #         "code": error_code_sensitive_answer,
                # }
                return
            # elif decision == "Further":
            # continue
            elif decision == "PASS":
                res = " ".join(tokens)
                yield {"content": res}
                logger.debug(res, end="")
            ###################################### 输出接入nova检测敏感词,结束流式#######################################

    async def stage5_follow_up_question(self, raw_query: str, tts_content: str) -> FollowUpQuestionResultModel:
        # memory = self.public_memory.get_past_messages()
        error_data = None
        query = raw_query + "\n" + tts_content + "\n"
        follow_up_prompt = follow_up_prompts.format(chat_his=query)
        # 没有system prompt
        messages = [{"role": "user", "content": follow_up_prompt}]
        # logger.info(f"messages:   {messages}")
        state5_result = None
        try:
            state5_result = await self.intent_llm.chat(
                messages,
                stream=False,
            )
        except BaseException as e:
            from constance.constance_value import error_code_relative_http_error
            error_data = ErrorDataModel(error_code=error_code_relative_http_error,
                                        error_message=f"Chat follow up http error, e: {str(e)}")
        return FollowUpQuestionResultModel(result_str=state5_result, error_info=error_data)

    async def stage5_conclusion_title(self, raw_query: str, tts_content: str) -> ConclusionTitleResultModel:
        error_data = None
        from prompts.prompt_conclusion_title import conclusion_prompts
        query = raw_query + "\n" + tts_content + "\n"
        conclusion_prompt = conclusion_prompts.format(chat_his=query)
        # 没有system prompt
        messages = [{"role": "user", "content": conclusion_prompt}]
        # logger.info(f"messages:   {messages}")
        state5_result = None
        try:
            state5_result = await self.summary_llm.chat(
                messages,
                stream=False,
            )
        except BaseException as e:
            from constance.constance_value import error_code_conclusion_title_http_error
            error_data = ErrorDataModel(error_code=error_code_conclusion_title_http_error,
                                        error_message=f"Chat conclusion title http error, e: {str(e)}")
        return ConclusionTitleResultModel(result=state5_result, error_info=error_data)

    async def stage5_follow_up_question_http(self):
        # memory = self.summary_llm.memory.get_all_messages()
        query = (
                self.public_memory.get_past_messages()[-2]["content"]
                + "\n"
                + self.public_memory.get_past_messages()[-1]["content"]
                + "\n"
        )
        query = follow_up_prompts.format(chat_his=query)
        full_response = ""
        async for chunk in self.intent_llm.yield_chat(query):
            full_response += chunk["content"]
            yield chunk["content"]

    def convert_query_with_link_to_frontend_ref(self, data):
        results = []
        i = 1
        for key, articles in data.items():
            for _, article in articles.items():
                result = {
                    "index": i,
                    "title": article["title"],
                    "url": article["link"],
                    "url_source": "",
                    "icon": "",
                }
                if "wiki" in result["title"] or result["title"] == "":
                    continue
                results.append(result)
                i += 1
        return results

    @staticmethod
    async def pure_web_search(query: str, tools: List[WebSearch], search_nums=10):
        action_executor = ActionExecutor(tools)

        tasks = []
        tasks.append(action_executor("web_search", query, search_nums))
        results = await asyncio.gather(*tasks)

        temp_results = {}
        temp_results[query] = results[0]
        query_with_link, fields_data, query_snippets, query_links = (
            stage1_format2query_with_link(
                temp_results, cached_link_first=False, include_title=False
            )
        )
        return query_with_link

    async def chat(self, request: WebChatQueryRequest):
        query = request.query
        position = request.position
        user_portrait = request.user_portrait
        message_id = request.message_id
        request_id = request.request_id
        model_name = request.model_name
        resource = request.resource
        start_time = request.start_time
        try:
            ####################################### 输入接入nova检测敏感词#######################################
            session_id = str(uuid.uuid4())
            user_id = "user_001"  # 车舱使用
            ext_info = {
                "model": model_name,
                "resource": resource,
                "user_id": user_id,
            }
            if self.detect:
                request_data = TextModerationRequest(
                    self.sensitive_config["ak"],
                    self.sensitive_config["sk"],
                    self.sensitive_config["app_id"],
                    user_id,
                    query,
                    "LLMPrompt",
                    session_id,
                    ext_info=ext_info,
                )
                result = await moderate_text_generic(
                    str(request_id), request_data, "输入"
                )
                if result:
                    response = {
                        "data": " 。(尊敬的用户您好，目前暂时无法回答这个话题哦～)",
                        "type": "message",
                        "messageId": message_id,
                    }
                    yield response
                    # from constance.constance_value import error_code_sensitive_query
                    # yield {
                    #     "data": f"The query ({query}) contains something sensitive.",
                    #     "type": "error",
                    #     "code": error_code_sensitive_query,
                    #     "messageId": message_id,
                    # }
                    return
            # TODO: -------------------> 下述moderation请求，在stage4作校验？
            ####################################### 输出接入nova检测敏感词，入参定义#######################################
            text_id = str(uuid.uuid4())
            request_data = TextModerationRequest(
                self.sensitive_config["ak"],
                self.sensitive_config["sk"],
                self.sensitive_config["app_id"],
                user_id,
                "",
                "LLMStreamResponse",
                session_id,
                text_id=text_id,
                finish_detect=False,
                ext_info=ext_info,
            )
            ####################################### 输出接入nova检测敏感词，入参定义#######################################

            llm_responds = ""

            stage0_intent_rewrite_task = asyncio.create_task(self.stage0_intent_rewrite(query, position, user_portrait))
            logger.info(f"intent start")
            intent_json_output_model = await stage0_intent_rewrite_task
            intent_json_output = intent_json_output_model.result
            intent_json_output_error =  intent_json_output_model.error_info
            logger.info(f"intent_json_output: {intent_json_output}, took {round(time.perf_counter() - start_time, 4)}s")
            logger.info("stage0_intent_rewrite_result FINISH")

            if isinstance(intent_json_output_error, ErrorDataModel):
                yield {
                    "type": "error",
                    "code": intent_json_output_error.error_code,
                    "data": intent_json_output_error.error_message,
                    "messageId": message_id,
                }

            if len(intent_json_output[query]) > 3:
                intent_json_output[query] = intent_json_output[query][:2]  # TODO: -------------------> 只取前两个关键字？

            llm_summary_fist_word_time = 0
            llm_summary_fist_word_time_detect = 0
            llm_summary_start = 0
            code = 0
            search_list = intent_json_output.get(query, [])
            if not search_list or 0 >= len(search_list):
                from constance.constance_value import error_code_rewrite_search_list_empty
                err_code = {
                    "data": "search_list(keywords) is null or empty",
                    "code" : error_code_rewrite_search_list_empty,
                    "type": "error",
                    "messageId": message_id,
                }
                yield err_code
                return
            
            keywords = {
                "data": search_list,
                "type": "intent",
                "messageId": message_id,
            }
            yield keywords

            src_start_time = time.perf_counter()
            logger.info("src start")
            stage1_task = asyncio.create_task(self.stage1_web_search(search_list))
            (
                query_with_link,
                fields_data,
                query_snippets,
                query_links,
                src_error_data
            ) = await stage1_task
            logger.info(f"src end-----cost time: {round(time.perf_counter() - src_start_time, 4)}s-----link: {query_with_link}")
            logger.info("stage1_web_search FINISH")
            if all(not value for value in query_with_link.values()):
                from constance.constance_value import error_code_src_search_empty
                err_code = {
                    "data": "There is nothing from web search.",
                    "code": error_code_src_search_empty,
                    "type": "error",
                    "messageId": message_id,
                }
                yield err_code
                    
                return

            if self.topk_method is None:  # mode 为 light
                guiding_res = ""  # await stage0_guide_task
                logger.info(f"stage0_guide result: {guiding_res}")

                frontend_ref = self.convert_query_with_link_to_frontend_ref(
                    query_with_link
                )
                logger.info(f"frontend_ref: {frontend_ref}")
                sources = {
                    "data": frontend_ref,
                    "type": "src",
                    "messageId": message_id,
                }

                yield sources
                llm_whole_ref = stage3_format_rest_query_with_link(
                    query_with_link, 0
                )
                async for chunk in self.stage4_llm_summary(
                    query,
                    llm_whole_ref,
                    "None",
                    request_id,
                    request_data,
                    guiding_res,
                    search=False
                ):
                    if "content" not in chunk:
                        chunk['messageId'] = message_id
                        yield chunk
                        continue
                    llm_responds += chunk["content"]
                    result = {
                        "data": chunk["content"],
                        "type": "message",
                        "messageId": message_id,
                    }
                    if "usage" in chunk:
                        result["usage"] = chunk["usage"]
                    yield result

                logger.info(f"stage4 result: {llm_responds}")
                # return llm_responds, query_with_link, intent_json_output
            else:
                frontend_ref = self.convert_query_with_link_to_frontend_ref(
                    query_with_link
                )
                logger.info(f"frontend_ref: {frontend_ref}")
                sources = {
                    "data": frontend_ref,
                    "type": "src",
                    "messageId": message_id,
                }
                yield sources
                llm_main_ref, llm_rest_ref, ll_ref_error_data = await self.stage2_3(
                    query, query_with_link, query_snippets, query_links
                )
                if isinstance(ll_ref_error_data, ErrorDataModel):
                    yield {
                        "type": "error",
                        "code": ll_ref_error_data.error_code,
                        "data": ll_ref_error_data.error_message,
                        "messageId": message_id,
                    }
                llm_whole_ref = llm_main_ref + llm_rest_ref
                fetch = {
                    "data": llm_whole_ref,
                    "type": "fetch",
                    "messageId": message_id,
                }
                yield fetch

                logger.info("llm_summary start")
                # 此处非light模式不用guide
                async for chunk in self.stage4_llm_summary(
                    query,
                    llm_whole_ref,
                    llm_rest_ref,
                    request_id,
                    request_data,
                    search=True,
                    position=position,
                ):
                    if "type" in chunk:
                        if "t6-mft" == chunk["type"]:
                            llm_summary_fist_word_time = round(time.perf_counter() - llm_summary_start,4)
                        elif "t7-sft" == chunk["type"]:
                            llm_summary_fist_word_time_detect = round(time.perf_counter() - llm_summary_start,4)
                    if "content" not in chunk:
                        chunk['messageId'] = message_id
                        yield chunk
                        continue
                    llm_responds += chunk["content"]
                    result = {
                        "data": chunk["content"],
                        "type": "message",
                        "messageId": message_id,
                    }
                    if chunk.get("code", 0) != 0:
                        error_msg = f"Chat summary error, code: {chunk['code']}"
                        from constance.constance_value import error_code_chat_summary_llm_error
                        yield {
                            "type": "error",
                            "code": error_code_chat_summary_llm_error,
                            "data": error_msg,
                            "messageId": message_id,

                        }
                        result["code"] = chunk["code"]
                        result["type"] = "error"
                        code = chunk["code"]
                    if "usage" in chunk:
                        result["usage"] = chunk["usage"]
                    yield result

            logger.info("conclusion start")
            conclusion_title_model = await self.stage5_conclusion_title(raw_query=query, tts_content=llm_responds)
            if conclusion_title_model.error_info:
                yield {
                    "type": "error",
                    "code": conclusion_title_model.error_info.error_code,
                    "data": conclusion_title_model.error_info.error_message,
                    "messageId": message_id,

                }
            conclusion_title_str = conclusion_title_model.result
            logger.info(f"conclusion end, result: {conclusion_title_str}")

            logger.info("follow_up_question start")
            follow_up_question_model = await self.stage5_follow_up_question(raw_query=query, tts_content=llm_responds)
            if follow_up_question_model.error_info:
                yield {
                    "type": "error",
                    "code": follow_up_question_model.error_info.error_code,
                    "data": follow_up_question_model.error_info.error_message,
                    "messageId": message_id,
                }
            follow_up_question_str = follow_up_question_model.result_str
            logger.info(f"follow_up_question end, result: {follow_up_question_str}")
            try:
                follow_up_questions = json.loads(follow_up_question_str)
                if isinstance(follow_up_questions, list):
                    if 3 == len(follow_up_questions):
                        follow_up_to_yield = {
                            "data": follow_up_questions,
                            "type": "follow_up",
                            "messageId": message_id,
                        }
                        if conclusion_title_str:
                            follow_up_to_yield["conclusion_title"] = conclusion_title_str
                        yield follow_up_to_yield
                    else:
                        follow_up_questions_list = []
                        for question_item in follow_up_questions:
                            if "|" in question_item:
                                split_question_item = question_item.split("|")
                                follow_up_questions_list += split_question_item
                        if 3 < len(follow_up_questions):
                            follow_up_questions_list = follow_up_questions_list[0:2]
                        follow_up_to_yield = {
                            "data": follow_up_questions_list,
                            "type": "follow_up",
                            "messageId": message_id,
                        }
                        if conclusion_title_str:
                            follow_up_to_yield["conclusion_title"] = conclusion_title_str
                        yield follow_up_to_yield

            except Exception as e:
                try:
                    follow_up_questions = follow_up_question_str.split("|")
                    if 3 < len(follow_up_questions):
                        follow_up_questions = follow_up_questions[0:2]
                    if isinstance(follow_up_questions, list):
                        follow_up_to_yield = {
                            "data": follow_up_questions,
                            "type": "follow_up",
                            "messageId": message_id,
                        }
                        if conclusion_title_str:
                            follow_up_to_yield["conclusion_title"] = conclusion_title_str
                        yield follow_up_to_yield
                except BaseException as e:
                    if conclusion_title_str:
                        yield {
                            "conclusion_title": conclusion_title_str,
                            "type": "follow_up",
                            "messageId": message_id,
                        }
                    from constance.constance_value import error_code_relative_format_error
                    yield {
                        "type": "error",
                        "code": error_code_relative_format_error,
                        "data": f"Event follow up format error, raw data: {follow_up_question_str}",
                        "messageId": message_id,
                    }


            result = {"type": "messageEnd", "messageId": message_id, "code": code}

            logger.info(
                f"llm_summary end-----detect:{self.detect}-----first token time: {llm_summary_fist_word_time if not self.detect else llm_summary_fist_word_time_detect}-----total time: {round(time.perf_counter() - llm_summary_start, 4)}")

            yield result
        except openai.BadRequestError as e:
            err_code = e.code
            message = e.response.json().get('error', {'message': ""}).get('message')
            result = {"type": "messageEnd", "messageId": message_id, "data": message, "code": err_code}
            yield result

        except Exception as e:
            logger.info(e)
            import traceback

            traceback.print_exc()
